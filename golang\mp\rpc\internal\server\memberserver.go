// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: member.proto

package server

import (
	"context"

	memberLogic "mp/rpc/internal/logic/member"
	memberTagLogic "mp/rpc/internal/logic/memberTag"
	tagLogic "mp/rpc/internal/logic/tag"
	"mp/rpc/internal/svc"
	"mp/rpc/member"
)

type MemberServer struct {
	svcCtx *svc.ServiceContext
	member.UnimplementedMemberServer
}

func NewMemberServer(svcCtx *svc.ServiceContext) *MemberServer {
	return &MemberServer{
		svcCtx: svcCtx,
	}
}

// 会员管理
func (s *MemberServer) GetMember(ctx context.Context, in *member.GetMemberReq) (*member.MemberInfo, error) {
	l := memberLogic.NewGetMemberLogic(ctx, s.svcCtx)
	return l.GetMember(in)
}

func (s *MemberServer) ListMembers(ctx context.Context, in *member.ListMembersReq) (*member.ListMembersResp, error) {
	l := memberLogic.NewListMembersLogic(ctx, s.svcCtx)
	return l.ListMembers(in)
}

func (s *MemberServer) CreateMember(ctx context.Context, in *member.CreateMemberReq) (*member.MemberInfo, error) {
	l := memberLogic.NewCreateMemberLogic(ctx, s.svcCtx)
	return l.CreateMember(in)
}

func (s *MemberServer) UpdateMember(ctx context.Context, in *member.UpdateMemberReq) (*member.MemberInfo, error) {
	l := memberLogic.NewUpdateMemberLogic(ctx, s.svcCtx)
	return l.UpdateMember(in)
}

func (s *MemberServer) DeleteMember(ctx context.Context, in *member.DeleteMemberReq) (*member.CommonResp, error) {
	l := memberLogic.NewDeleteMemberLogic(ctx, s.svcCtx)
	return l.DeleteMember(in)
}

// 标签管理
func (s *MemberServer) GetTag(ctx context.Context, in *member.GetTagReq) (*member.TagInfo, error) {
	l := tagLogic.NewGetTagLogic(ctx, s.svcCtx)
	return l.GetTag(in)
}

func (s *MemberServer) ListTags(ctx context.Context, in *member.ListTagsReq) (*member.ListTagsResp, error) {
	l := tagLogic.NewListTagsLogic(ctx, s.svcCtx)
	return l.ListTags(in)
}

func (s *MemberServer) CreateTag(ctx context.Context, in *member.CreateTagReq) (*member.TagInfo, error) {
	l := tagLogic.NewCreateTagLogic(ctx, s.svcCtx)
	return l.CreateTag(in)
}

func (s *MemberServer) UpdateTag(ctx context.Context, in *member.UpdateTagReq) (*member.TagInfo, error) {
	l := tagLogic.NewUpdateTagLogic(ctx, s.svcCtx)
	return l.UpdateTag(in)
}

func (s *MemberServer) DeleteTag(ctx context.Context, in *member.DeleteTagReq) (*member.CommonResp, error) {
	l := tagLogic.NewDeleteTagLogic(ctx, s.svcCtx)
	return l.DeleteTag(in)
}

// 会员标签关联
func (s *MemberServer) AddTagsToMember(ctx context.Context, in *member.AddTagsToMemberReq) (*member.CommonResp, error) {
	l := memberTagLogic.NewAddTagsToMemberLogic(ctx, s.svcCtx)
	result, err := l.AddTagsToMember(in)
	if err != nil {
		return nil, err
	}

	// 创建一个新的CommonResp对象
	resp := &member.CommonResp{}

	// 如果result是*memberTagLogic.CommonResp类型，则复制其字段
	if commonResp, ok := result.(*memberTagLogic.CommonResp); ok {
		resp.Success = commonResp.Success
		resp.Message = commonResp.Message
	}

	return resp, nil
}

func (s *MemberServer) RemoveTagsFromMember(ctx context.Context, in *member.RemoveTagsFromMemberReq) (*member.CommonResp, error) {
	l := memberTagLogic.NewRemoveTagsFromMemberLogic(ctx, s.svcCtx)
	return l.RemoveTagsFromMember(in)
}

func (s *MemberServer) GetMemberTags(ctx context.Context, in *member.GetMemberTagsReq) (*member.ListTagsResp, error) {
	l := memberTagLogic.NewGetMemberTagsLogic(ctx, s.svcCtx)
	return l.GetMemberTags(in)
}
