package tag

import (
	"context"
	"fmt"
	"mp/rpc/model"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTagLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTagLogic {
	return &GetTagLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetTagLogic) GetTag(in *member.GetTagReq) (*member.TagInfo, error) {
	// todo: add your logic here and delete this line

	// 检查ID是否有效
	if in.Id < 0 {
		return nil, fmt.Errorf("无效的ID: %d", in.Id)
	}

	var tagData model.MemberTag
	result := l.svcCtx.DB.First(&tagData, in.Id)
	if result.Error != nil {
		return nil, fmt.Errorf("查询数据库失败: %v", result.Error)
	}

	// 如果没有找到该数据
	if result.RowsAffected == 0 {
		return nil, fmt.Errorf("未找到ID为%d的会员记录", in.Id)
	}
	return &member.TagInfo{
		Id:         int64(tagData.ID),
		MerchantId: int64(tagData.MerchantID),
		StoreId:    int64(tagData.StoreID),
		Title:      tagData.Title,
		Sort:       int32(tagData.Sort),
		Status:     int32(tagData.Status),
		CreatedAt:  int64(tagData.CreatedAt),
		UpdatedAt:  int64(tagData.UpdatedAt),
	}, nil

}
