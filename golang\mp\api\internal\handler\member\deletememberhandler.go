package member

import (
	"net/http"

	"mp/api/internal/logic/member"
	"mp/api/internal/svc"
	"mp/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 删除会员
func DeleteMemberHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DeleteMemberReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := member.NewDeleteMemberLogic(r.Context(), svcCtx)
		resp, err := l.DeleteMember(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
