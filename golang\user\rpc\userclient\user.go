// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: user.proto

package userclient

import (
	"context"

	"user/rpc/user"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	GetUserRequest  = user.GetUserRequest
	LoginRequest    = user.LoginRequest
	RegisterRequest = user.RegisterRequest
	UserResponse    = user.UserResponse

	User interface {
		// Register creates a new user
		Register(ctx context.Context, in *RegisterRequest, opts ...grpc.CallOption) (*UserResponse, error)
		// Login authenticates a user and returns user information with token
		Login(ctx context.Context, in *LoginRequest, opts ...grpc.CallOption) (*UserResponse, error)
		// GetUser retrieves user information by ID
		GetUser(ctx context.Context, in *GetUserRequest, opts ...grpc.CallOption) (*UserResponse, error)
	}

	defaultUser struct {
		cli zrpc.Client
	}
)

func NewUser(cli zrpc.Client) User {
	return &defaultUser{
		cli: cli,
	}
}

// Register creates a new user
func (m *defaultUser) Register(ctx context.Context, in *RegisterRequest, opts ...grpc.CallOption) (*UserResponse, error) {
	client := user.NewUserClient(m.cli.Conn())
	return client.Register(ctx, in, opts...)
}

// Login authenticates a user and returns user information with token
func (m *defaultUser) Login(ctx context.Context, in *LoginRequest, opts ...grpc.CallOption) (*UserResponse, error) {
	client := user.NewUserClient(m.cli.Conn())
	return client.Login(ctx, in, opts...)
}

// GetUser retrieves user information by ID
func (m *defaultUser) GetUser(ctx context.Context, in *GetUserRequest, opts ...grpc.CallOption) (*UserResponse, error) {
	client := user.NewUserClient(m.cli.Conn())
	return client.GetUser(ctx, in, opts...)
}
