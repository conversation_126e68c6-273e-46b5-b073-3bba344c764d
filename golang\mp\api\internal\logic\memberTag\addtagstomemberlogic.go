package memberTag

import (
	"context"
	"fmt"
	"mp/rpc/member"

	"mp/api/internal/svc"
	"mp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddTagsToMemberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 为会员添加标签
func NewAddTagsToMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddTagsToMemberLogic {
	return &AddTagsToMemberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddTagsToMemberLogic) AddTagsToMember(req *types.AddTagsToMemberReq) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	_, err = l.svcCtx.MemberRpc.AddTagsToMember(l.ctx, &member.AddTagsToMemberReq{
		MemberId: req.MemberId,
		TagIds:   req.TagIds,
	})
	if err != nil {
		return nil, fmt.Errorf("调用 RPC AddTagsToMember 服务 失败 %v", err)
	}

	return &types.CommonResponse{
		Code:    200,
		Message: "调用 RPC AddTagsToMember 服务 成功",
	}, nil
}
