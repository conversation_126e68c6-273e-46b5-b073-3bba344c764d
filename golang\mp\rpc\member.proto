syntax = "proto3";

package member;
option go_package="./member";

// 会员服务定义
service Member {
  // 会员管理
  rpc GetMember(GetMemberReq) returns (MemberInfo);
  rpc ListMembers(ListMembersReq) returns (ListMembersResp);
  rpc CreateMember(CreateMemberReq) returns (MemberInfo);
  rpc UpdateMember(UpdateMemberReq) returns (MemberInfo);
  rpc DeleteMember(DeleteMemberReq) returns (CommonResp);
  
  // 标签管理
  rpc GetTag(GetTagReq) returns (TagInfo);
  rpc ListTags(ListTagsReq) returns (ListTagsResp);
  rpc CreateTag(CreateTagReq) returns (TagInfo);
  rpc UpdateTag(UpdateTagReq) returns (TagInfo);
  rpc DeleteTag(DeleteTagReq) returns (CommonResp);
  
  // 会员标签关联
  rpc AddTagsToMember(AddTagsToMemberReq) returns (CommonResp);
  rpc RemoveTagsFromMember(RemoveTagsFromMemberReq) returns (CommonResp);
  rpc GetMemberTags(GetMemberTagsReq) returns (ListTagsResp);
}

// 通用响应
message CommonResp {
  bool success = 1;
  string message = 2;
}

// 会员相关消息
message MemberInfo {
  int64 id = 1;
  int64 merchant_id = 2;
  int64 store_id = 3;
  string username = 4;
  string realname = 5;
  string nickname = 6;
  string head_portrait = 7;
  int32 gender = 8;
  string qq = 9;
  string email = 10;
  string birthday = 11;
  int64 province_id = 12;
  int64 city_id = 13;
  int64 area_id = 14;
  string address = 15;
  string mobile = 16;
  string tel_no = 17;
  string bg_image = 18;
  string description = 19;
  int32 visit_count = 20;
  int64 last_time = 21;
  string last_ip = 22;
  int32 role = 23;
  int32 current_level = 24;
  int64 level_expiration_time = 25;
  int32 level_buy_type = 26;
  int64 pid = 27;
  int32 level = 28;
  string tree = 29;
  string promoter_code = 30;
  int32 certification_type = 31;
  string source = 32;
  int32 status = 33;
  int64 created_at = 34;
  int64 updated_at = 35;
  int64 region_id = 36;
}

message GetMemberReq {
  int64 id = 1;
}

message ListMembersReq {
  int64 merchant_id = 1;
  int64 store_id = 2;
  string keyword = 3;
  int32 status = 4;
  int32 page = 5;
  int32 page_size = 6;
}

message ListMembersResp {
  int64 total = 1;
  repeated MemberInfo list = 2;
}

message CreateMemberReq {
  int64 merchant_id = 1;
  int64 store_id = 2;
  string username = 3;
  string password = 4;
  string realname = 5;
  string nickname = 6;
  string head_portrait = 7;
  int32 gender = 8;
  string qq = 9;
  string email = 10;
  string birthday = 11;
  int64 province_id = 12;
  int64 city_id = 13;
  int64 area_id = 14;
  string address = 15;
  string mobile = 16;
  string tel_no = 17;
  string bg_image = 18;
  string description = 19;
  int32 role = 20;
  int32 current_level = 21;
  int32 level_buy_type = 22;
  int64 pid = 23;
  int32 certification_type = 24;
  string source = 25;
  int32 status = 26;
  int64 region_id = 27;
}

message UpdateMemberReq {
  int64 id = 1;
  string realname = 2;
  string nickname = 3;
  string head_portrait = 4;
  int32 gender = 5;
  string qq = 6;
  string email = 7;
  string birthday = 8;
  int64 province_id = 9;
  int64 city_id = 10;
  int64 area_id = 11;
  string address = 12;
  string mobile = 13;
  string tel_no = 14;
  string bg_image = 15;
  string description = 16;
  int32 role = 17;
  int32 current_level = 18;
  int32 level_buy_type = 19;
  int64 pid = 20;
  int32 certification_type = 21;
  string source = 22;
  int32 status = 23;
  int64 region_id = 24;
}

message DeleteMemberReq {
  int64 id = 1;
}

// 标签相关消息
message TagInfo {
  int64 id = 1;
  int64 merchant_id = 2;
  int64 store_id = 3;
  string title = 4;
  int32 sort = 5;
  int32 status = 6;
  int64 created_at = 7;
  int64 updated_at = 8;
}

message GetTagReq {
  int64 id = 1;
}

message ListTagsReq {
  int64 merchant_id = 1;
  int64 store_id = 2;
  string keyword = 3;
  int32 status = 4;
  int32 page = 5;
  int32 page_size = 6;
}

message ListTagsResp {
  int64 total = 1;
  repeated TagInfo list = 2;
}

message CreateTagReq {
  int64 merchant_id = 1;
  int64 store_id = 2;
  string title = 3;
  int32 sort = 4;
  int32 status = 5;
}

message UpdateTagReq {
  int64 id = 1;
  string title = 2;
  int32 sort = 3;
  int32 status = 4;
}

message DeleteTagReq {
  int64 id = 1;
}

// 会员标签关联相关消息
message AddTagsToMemberReq {
  int64 member_id = 1;
  repeated int64 tag_ids = 2;
}

message RemoveTagsFromMemberReq {
  int64 member_id = 1;
  repeated int64 tag_ids = 2;
}

message GetMemberTagsReq {
  int64 member_id = 1;
} 