package memberTag

import (
	"context"
	"fmt"
	"mp/rpc/member"

	"mp/api/internal/svc"
	"mp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RemoveTagsFromMemberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 移除会员标签
func NewRemoveTagsFromMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveTagsFromMemberLogic {
	return &RemoveTagsFromMemberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RemoveTagsFromMemberLogic) RemoveTagsFromMember(req *types.RemoveTagsFromMemberReq) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	_, err = l.svcCtx.MemberRpc.RemoveTagsFromMember(l.ctx, &member.RemoveTagsFromMemberReq{
		MemberId: req.MemberId,
		TagIds:   req.TagIds,
	})
	if err != nil {
		return nil, fmt.Errorf("调用 RPC RemoveTagsFromMember 服务 失败 %v", err)
	}
	return &types.CommonResponse{
		Code:    200,
		Message: "调用 RPC RemoveTagsFromMember 服务 成功",
	}, nil
}
