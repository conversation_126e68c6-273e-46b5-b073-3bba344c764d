-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4
-- https://www.phpmyadmin.net/
--
-- 主机： mysql-db
-- 生成日期： 2025-07-24 11:21:17
-- 服务器版本： 5.7.44
-- PHP 版本： 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `mp_db`
--

-- --------------------------------------------------------

--
-- 表的结构 `api_access_token`
--

CREATE TABLE `api_access_token` (
  `id` int(10) UNSIGNED NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `refresh_token` varchar(60) DEFAULT '' COMMENT '刷新令牌',
  `access_token` varchar(60) DEFAULT '' COMMENT '授权令牌',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '用户类型',
  `group` varchar(100) DEFAULT '' COMMENT '组别',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api_授权秘钥表';

-- --------------------------------------------------------

--
-- 表的结构 `common_action_log`
--

CREATE TABLE `common_action_log` (
  `id` int(11) NOT NULL COMMENT '主键',
  `app_id` varchar(50) DEFAULT '' COMMENT '应用id',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(11) DEFAULT '0' COMMENT '用户id',
  `member_name` varchar(100) DEFAULT '' COMMENT '用户id',
  `method` varchar(20) DEFAULT '' COMMENT '提交类型',
  `module` varchar(50) DEFAULT '' COMMENT '模块',
  `controller` varchar(100) DEFAULT '' COMMENT '控制器',
  `action` varchar(50) DEFAULT '' COMMENT '方法',
  `url` varchar(255) DEFAULT '' COMMENT '提交url',
  `get_data` json DEFAULT NULL COMMENT 'get数据',
  `post_data` json DEFAULT NULL COMMENT 'post数据',
  `header_data` json DEFAULT NULL COMMENT 'header数据',
  `behavior` varchar(50) DEFAULT '' COMMENT '行为类别',
  `remark` varchar(1000) DEFAULT '' COMMENT '日志备注',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `map_id` int(11) DEFAULT '0' COMMENT '关联ID',
  `map_data` json DEFAULT NULL COMMENT '关联数据',
  `country` varchar(100) DEFAULT '' COMMENT '国家',
  `provinces` varchar(100) DEFAULT '' COMMENT '省',
  `city` varchar(100) DEFAULT '' COMMENT '城市',
  `device` varchar(200) DEFAULT '' COMMENT '设备信息',
  `ip` varchar(40) DEFAULT '' COMMENT 'ip地址',
  `req_id` varchar(50) DEFAULT '' COMMENT '对外id',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(11) DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_行为表';

-- --------------------------------------------------------

--
-- 表的结构 `common_addons`
--

CREATE TABLE `common_addons` (
  `id` int(11) NOT NULL COMMENT '主键',
  `title` varchar(20) NOT NULL DEFAULT '' COMMENT '中文名',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '插件名或标识',
  `title_initial` varchar(1) NOT NULL DEFAULT '' COMMENT '首字母拼音',
  `bootstrap` varchar(255) DEFAULT '' COMMENT '启用文件',
  `service` varchar(255) DEFAULT '' COMMENT '服务调用类',
  `cover` varchar(200) DEFAULT '' COMMENT '封面',
  `group` varchar(20) DEFAULT '' COMMENT '组别',
  `brief_introduction` varchar(140) DEFAULT '' COMMENT '简单介绍',
  `description` varchar(1000) DEFAULT '' COMMENT '插件描述',
  `author` varchar(40) DEFAULT '' COMMENT '作者',
  `version` varchar(20) DEFAULT '1.0.0' COMMENT '版本号',
  `is_merchant_route_map` tinyint(1) DEFAULT '0' COMMENT '商户路由映射',
  `default_config` json DEFAULT NULL COMMENT '默认配置',
  `console` json DEFAULT NULL COMMENT '控制台',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_插件表';

-- --------------------------------------------------------

--
-- 表的结构 `common_addons_config`
--

CREATE TABLE `common_addons_config` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `app_id` varchar(20) NOT NULL DEFAULT '' COMMENT '应用',
  `addon_name` varchar(100) NOT NULL DEFAULT '' COMMENT '插件名或标识',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `data` json DEFAULT NULL COMMENT '配置'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_插件配置值表';

-- --------------------------------------------------------

--
-- 表的结构 `common_archives`
--

CREATE TABLE `common_archives` (
  `id` int(11) NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `member_id` int(11) DEFAULT '0' COMMENT '申请人',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '用户类型',
  `certification_type` tinyint(4) DEFAULT '1' COMMENT '认证类型[1:公司;2:个人]',
  `profit_type` tinyint(4) DEFAULT '1' COMMENT '盈利类型[1:私立;2:国有]',
  `company_name` varchar(255) DEFAULT '' COMMENT '公司名称',
  `unified_social_credit_code` varchar(200) DEFAULT '' COMMENT '统一社会信用代码',
  `business_license` varchar(255) DEFAULT '' COMMENT '营业执照',
  `business_scope` varchar(3000) DEFAULT NULL COMMENT '经营范围',
  `practice_qualification_certificate` varchar(255) DEFAULT '' COMMENT '执业资格证',
  `establish_year` date DEFAULT NULL COMMENT '成立年份',
  `floor_space` double DEFAULT '0' COMMENT '占地面积',
  `content` mediumtext COMMENT '详情',
  `corporate_realname` varchar(100) DEFAULT '' COMMENT '法人真实姓名',
  `corporate_mobile` varchar(50) DEFAULT '' COMMENT '法人手机号码',
  `corporate_identity_card` varchar(30) DEFAULT '' COMMENT '法人身份证',
  `corporate_identity_card_front` varchar(255) DEFAULT '' COMMENT '法人身份证正面(国徽)',
  `corporate_identity_card_back` varchar(255) DEFAULT '' COMMENT '法人身份证反面(人面)',
  `bank_account_name` varchar(100) DEFAULT '' COMMENT '公司银行开户名',
  `bank_account_number` varchar(100) DEFAULT '' COMMENT '公司银行账号',
  `bank_branch_name` varchar(100) DEFAULT '' COMMENT '开户银行支行名称',
  `bank_location` varchar(100) DEFAULT '' COMMENT '开户银行所在地',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_认证信息';

-- --------------------------------------------------------

--
-- 表的结构 `common_archives_apply`
--

CREATE TABLE `common_archives_apply` (
  `id` int(11) NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `member_id` int(11) DEFAULT NULL COMMENT '申请人',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `certification_type` tinyint(4) DEFAULT '1' COMMENT '认证类型[1:公司;2:个人]',
  `profit_type` tinyint(4) DEFAULT '1' COMMENT '盈利类型[1:私立;2:国有]',
  `company_name` varchar(255) DEFAULT '' COMMENT '公司名称',
  `unified_social_credit_code` varchar(200) DEFAULT '' COMMENT '统一社会信用代码',
  `business_license` varchar(255) DEFAULT '' COMMENT '营业执照',
  `business_scope` varchar(3000) DEFAULT '' COMMENT '经营范围',
  `practice_qualification_certificate` varchar(255) DEFAULT '' COMMENT '执业资格证',
  `establish_year` date DEFAULT NULL COMMENT '成立年限',
  `floor_space` double DEFAULT '0' COMMENT '占地面积',
  `content` mediumtext COMMENT '详情',
  `corporate_realname` varchar(50) DEFAULT '' COMMENT '法人真实姓名',
  `corporate_mobile` varchar(30) DEFAULT '' COMMENT '法人手机号码',
  `corporate_identity_card` varchar(100) DEFAULT '' COMMENT '法人身份证',
  `corporate_identity_card_front` varchar(255) DEFAULT '' COMMENT '法人身份证正面(国徽)',
  `corporate_identity_card_back` varchar(255) DEFAULT '' COMMENT '法人身份证反面(人面)',
  `bank_account_name` varchar(100) DEFAULT '' COMMENT '公司银行开户名',
  `bank_account_number` varchar(100) DEFAULT '' COMMENT '公司银行账号',
  `bank_branch_name` varchar(100) DEFAULT '' COMMENT '开户银行支行名称',
  `bank_location` varchar(100) DEFAULT '' COMMENT '开户银行所在地',
  `audit_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '审核状态[0:申请;1通过;-1失败]',
  `audit_time` int(10) UNSIGNED DEFAULT '0' COMMENT '审核时间',
  `refusal_cause` varchar(200) DEFAULT '' COMMENT '拒绝原因',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_认证信息申请';

-- --------------------------------------------------------

--
-- 表的结构 `common_attachment`
--

CREATE TABLE `common_attachment` (
  `id` int(11) NOT NULL,
  `member_id` int(11) DEFAULT '0' COMMENT '用户',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `cate_id` int(11) DEFAULT '0' COMMENT '分类',
  `drive` varchar(50) DEFAULT '' COMMENT '驱动',
  `upload_type` varchar(10) DEFAULT '' COMMENT '上传类型',
  `specific_type` varchar(100) NOT NULL DEFAULT 'image/jpeg' COMMENT '类别',
  `url` varchar(500) DEFAULT '' COMMENT 'url',
  `path` varchar(500) DEFAULT '' COMMENT '本地路径',
  `md5` varchar(100) DEFAULT '' COMMENT 'md5校验码',
  `name` varchar(500) DEFAULT '' COMMENT '文件原始名',
  `extension` varchar(100) DEFAULT 'jpg' COMMENT '扩展名',
  `size` int(11) DEFAULT '0' COMMENT '长度',
  `format_size` varchar(50) DEFAULT '' COMMENT '格式化长度',
  `year` int(10) UNSIGNED DEFAULT '0' COMMENT '年份',
  `month` int(11) DEFAULT '0' COMMENT '月份',
  `day` int(10) UNSIGNED DEFAULT '0' COMMENT '日',
  `width` int(10) UNSIGNED DEFAULT '0' COMMENT '宽度',
  `height` int(10) UNSIGNED DEFAULT '0' COMMENT '高度',
  `ip` varchar(16) DEFAULT '' COMMENT '上传者ip',
  `req_id` varchar(50) DEFAULT '' COMMENT '对外id',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_资源管理';

-- --------------------------------------------------------

--
-- 表的结构 `common_attachment_cate`
--

CREATE TABLE `common_attachment_cate` (
  `id` int(11) NOT NULL COMMENT '主键',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `title` varchar(100) DEFAULT '' COMMENT '标题',
  `type` varchar(30) DEFAULT '' COMMENT '类别',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `level` tinyint(1) DEFAULT '1' COMMENT '级别',
  `pid` int(11) DEFAULT '0' COMMENT '上级id',
  `tree` varchar(500) DEFAULT '' COMMENT '树',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态',
  `created_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COMMENT='公用_资源文件分类';

-- --------------------------------------------------------

--
-- 表的结构 `common_bank_number`
--

CREATE TABLE `common_bank_number` (
  `id` int(10) UNSIGNED NOT NULL,
  `bank_name` varchar(255) DEFAULT '' COMMENT '银行名称',
  `bank_number` varchar(255) DEFAULT '' COMMENT '银行编号',
  `type` tinyint(4) DEFAULT '1' COMMENT '银行卡类型：1:微信；2:支付宝',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态',
  `created_at` int(11) DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(11) DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_银行卡编号';

-- --------------------------------------------------------

--
-- 表的结构 `common_config`
--

CREATE TABLE `common_config` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '配置标题',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '配置标识',
  `app_id` varchar(20) NOT NULL DEFAULT '' COMMENT '应用',
  `type` varchar(30) NOT NULL DEFAULT '' COMMENT '配置类型',
  `cate_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '配置分类',
  `extra` varchar(1000) NOT NULL DEFAULT '' COMMENT '配置值',
  `remark` varchar(1000) NOT NULL DEFAULT '' COMMENT '配置说明',
  `is_hide_remark` tinyint(4) DEFAULT '1' COMMENT '是否隐藏说明',
  `default_value` varchar(500) DEFAULT '' COMMENT '默认配置',
  `sort` int(10) UNSIGNED DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_配置表';

-- --------------------------------------------------------

--
-- 表的结构 `common_config_cate`
--

CREATE TABLE `common_config_cate` (
  `id` int(11) NOT NULL COMMENT '主键',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题',
  `name` varchar(100) DEFAULT '' COMMENT '标识',
  `pid` int(10) UNSIGNED DEFAULT '0' COMMENT '上级id',
  `app_id` varchar(20) NOT NULL DEFAULT '' COMMENT '应用',
  `level` tinyint(3) UNSIGNED DEFAULT '1' COMMENT '级别',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `tree` varchar(300) NOT NULL DEFAULT '' COMMENT '树',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '添加时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_配置分类表';

-- --------------------------------------------------------

--
-- 表的结构 `common_config_value`
--

CREATE TABLE `common_config_value` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `app_id` varchar(20) NOT NULL DEFAULT '' COMMENT '应用',
  `config_id` int(11) NOT NULL DEFAULT '0' COMMENT '配置id',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `data` text COMMENT '配置内'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_配置值表';

-- --------------------------------------------------------

--
-- 表的结构 `common_log`
--

CREATE TABLE `common_log` (
  `id` int(11) NOT NULL,
  `app_id` varchar(50) DEFAULT '' COMMENT '应用id',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(11) DEFAULT '0' COMMENT '用户id',
  `member_name` varchar(100) DEFAULT '' COMMENT '用户名称',
  `method` varchar(20) DEFAULT '' COMMENT '提交类型',
  `module` varchar(50) DEFAULT '' COMMENT '模块',
  `controller` varchar(100) DEFAULT '' COMMENT '控制器',
  `action` varchar(50) DEFAULT '' COMMENT '方法',
  `url` varchar(1000) DEFAULT '' COMMENT '提交url',
  `get_data` json DEFAULT NULL COMMENT 'get数据',
  `post_data` json DEFAULT NULL COMMENT 'post数据',
  `header_data` json DEFAULT NULL COMMENT 'header数据',
  `ip` varchar(50) DEFAULT '' COMMENT 'ip地址',
  `error_code` int(11) DEFAULT '0' COMMENT '报错code',
  `error_msg` varchar(1000) DEFAULT '' COMMENT '报错信息',
  `error_data` json DEFAULT NULL COMMENT '报错日志',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `req_id` varchar(50) DEFAULT '' COMMENT '对外id',
  `device` varchar(200) DEFAULT '' COMMENT '设备信息',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(-1:已删除,0:禁用,1:正常)',
  `created_at` int(11) DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_日志';

-- --------------------------------------------------------

--
-- 表的结构 `common_menu`
--

CREATE TABLE `common_menu` (
  `id` int(11) NOT NULL,
  `title` varchar(50) DEFAULT '' COMMENT '标题',
  `name` varchar(50) DEFAULT '' COMMENT '标识',
  `app_id` varchar(20) DEFAULT '' COMMENT '应用',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `addon_location` varchar(50) DEFAULT '' COMMENT '插件显示位置',
  `cate_id` int(10) UNSIGNED DEFAULT '0' COMMENT '分类id',
  `pid` int(10) UNSIGNED DEFAULT '0' COMMENT '上级id',
  `url` varchar(100) DEFAULT '' COMMENT '路由',
  `icon` varchar(50) DEFAULT '' COMMENT '样式',
  `level` tinyint(3) UNSIGNED DEFAULT '1' COMMENT '级别',
  `dev` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '开发者[0:都可见;开发模式可见]',
  `sort` int(11) DEFAULT '999' COMMENT '排序',
  `params` json DEFAULT NULL COMMENT '参数',
  `pattern` json DEFAULT NULL COMMENT '开发可见模式',
  `tree` varchar(300) DEFAULT '' COMMENT '树',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '添加时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_菜单导航表';

-- --------------------------------------------------------

--
-- 表的结构 `common_menu_cate`
--

CREATE TABLE `common_menu_cate` (
  `id` int(11) NOT NULL COMMENT '主键',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题',
  `name` varchar(50) DEFAULT '' COMMENT '标识',
  `app_id` varchar(20) NOT NULL DEFAULT '' COMMENT '应用',
  `icon` varchar(50) DEFAULT '' COMMENT 'icon',
  `type` tinyint(4) DEFAULT '0' COMMENT '应用中心',
  `is_default_show` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '默认显示',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `addon_location` varchar(50) DEFAULT '' COMMENT '插件显示位置',
  `sort` int(11) DEFAULT '999' COMMENT '排序',
  `level` tinyint(3) UNSIGNED DEFAULT '1' COMMENT '级别',
  `tree` varchar(300) DEFAULT '' COMMENT '树',
  `pid` int(10) UNSIGNED DEFAULT '0' COMMENT '上级id',
  `pattern` json DEFAULT NULL COMMENT '开发可见模式',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '添加时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_菜单分类表';

-- --------------------------------------------------------

--
-- 表的结构 `common_notify`
--

CREATE TABLE `common_notify` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `title` varchar(150) DEFAULT '' COMMENT '标题',
  `content` varchar(300) DEFAULT '' COMMENT '消息内容',
  `type` tinyint(1) DEFAULT '0' COMMENT '消息类型[1:公告;2:提醒;3:信息(私信)',
  `target_id` int(11) DEFAULT '0' COMMENT '目标id',
  `target_type` varchar(100) DEFAULT '' COMMENT '目标类型',
  `target_display` int(11) DEFAULT '1' COMMENT '目标者是否删除',
  `action` varchar(100) DEFAULT '' COMMENT '动作',
  `view` int(11) DEFAULT '0' COMMENT '浏览量',
  `sender_id` int(11) DEFAULT '0' COMMENT '发送者id',
  `sender_display` tinyint(1) DEFAULT '1' COMMENT '发送者是否删除',
  `sender_revocation` tinyint(1) DEFAULT '1' COMMENT '是否撤回 0是撤回',
  `params` json DEFAULT NULL COMMENT '参数',
  `link` varchar(255) DEFAULT '' COMMENT '详情链接',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_消息公告表';

-- --------------------------------------------------------

--
-- 表的结构 `common_notify_announce`
--

CREATE TABLE `common_notify_announce` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `title` varchar(150) DEFAULT '' COMMENT '标题',
  `content` longtext COMMENT '消息内容',
  `cover` varchar(100) DEFAULT '' COMMENT '封面',
  `synopsis` varchar(255) DEFAULT '' COMMENT '概要',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_消息公告表';

-- --------------------------------------------------------

--
-- 表的结构 `common_notify_config`
--

CREATE TABLE `common_notify_config` (
  `id` int(11) NOT NULL,
  `member_id` int(11) DEFAULT '0' COMMENT '用户id',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `app_id` varchar(50) DEFAULT '' COMMENT '应用id',
  `name` varchar(100) DEFAULT '' COMMENT '标识',
  `title` varchar(100) DEFAULT '' COMMENT '标题',
  `content` text COMMENT '内容',
  `type` varchar(50) DEFAULT '' COMMENT '发送类型',
  `template_id` varchar(100) DEFAULT '' COMMENT '模板ID',
  `url` varchar(255) DEFAULT '' COMMENT '跳转地址',
  `params` json DEFAULT NULL COMMENT '参数',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_通知配置表';

-- --------------------------------------------------------

--
-- 表的结构 `common_notify_member`
--

CREATE TABLE `common_notify_member` (
  `id` int(11) NOT NULL,
  `app_id` varchar(50) DEFAULT '' COMMENT '应用id',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '管理员id',
  `notify_id` int(11) DEFAULT '0' COMMENT '消息id',
  `is_read` tinyint(4) DEFAULT '0' COMMENT '是否已读 1已读',
  `read_member_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '已读用户',
  `type` tinyint(1) DEFAULT '0' COMMENT '消息类型[1:公告;2:提醒;3:信息(私信)',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_消息查看时间记录表';

-- --------------------------------------------------------

--
-- 表的结构 `common_notify_pull_time`
--

CREATE TABLE `common_notify_pull_time` (
  `id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL COMMENT '管理员id',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `type` tinyint(4) DEFAULT '0' COMMENT '消息类型[1:公告;2:提醒;3:信息(私信)',
  `alert_type` varchar(20) DEFAULT '0' COMMENT '提醒消息类型[sys:系统;wechat:微信]',
  `last_time` int(11) DEFAULT NULL COMMENT '最后拉取时间',
  `last_id` int(11) DEFAULT NULL COMMENT '最后拉取ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_消息拉取表';

-- --------------------------------------------------------

--
-- 表的结构 `common_provinces`
--

CREATE TABLE `common_provinces` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '栏目名',
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '父栏目',
  `short_title` varchar(50) DEFAULT '' COMMENT '缩写',
  `area_code` int(11) DEFAULT '0' COMMENT '区域编码',
  `zip_code` int(11) DEFAULT '0' COMMENT '邮政编码',
  `pinyin` varchar(100) DEFAULT '' COMMENT '拼音',
  `lng` varchar(20) DEFAULT '' COMMENT '经度',
  `lat` varchar(20) DEFAULT '' COMMENT '纬度',
  `level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '级别',
  `tree` varchar(60) NOT NULL COMMENT '树',
  `sort` int(10) UNSIGNED DEFAULT '0' COMMENT '排序'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_省市区记录表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `common_report_log`
--

CREATE TABLE `common_report_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `log_id` int(10) UNSIGNED DEFAULT '0' COMMENT '公用日志id',
  `app_id` varchar(50) DEFAULT '' COMMENT '应用id',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户ID',
  `device_id` varchar(64) DEFAULT '' COMMENT '设备ID',
  `device_name` varchar(64) DEFAULT '' COMMENT '设备名称',
  `width` smallint(6) DEFAULT '0' COMMENT '屏幕宽度',
  `height` smallint(6) DEFAULT '0' COMMENT '屏幕高度',
  `os` varchar(64) DEFAULT '' COMMENT '操作系统',
  `os_version` varchar(64) DEFAULT '' COMMENT '操作系统版本',
  `is_root` tinyint(4) DEFAULT '0' COMMENT '是否越狱， 0:未越狱， 1:已越狱',
  `network` varchar(64) DEFAULT '' COMMENT '网络类型',
  `wifi_ssid` varchar(128) DEFAULT '' COMMENT 'wifi的编号',
  `wifi_mac` varchar(64) DEFAULT '' COMMENT 'WIFI的mac',
  `xyz` varchar(64) DEFAULT '' COMMENT '三轴加速度',
  `version_name` varchar(16) DEFAULT '' COMMENT 'APP版本名',
  `api_version` varchar(255) DEFAULT '' COMMENT 'API的版本号',
  `channel` varchar(64) DEFAULT '' COMMENT '渠道名',
  `app_name` tinyint(4) DEFAULT '0' COMMENT 'APP编号， 1:android， 3:iphone',
  `dpi` int(11) DEFAULT '0' COMMENT '屏幕密度',
  `api_level` int(11) DEFAULT '0' COMMENT 'android的API的版本号',
  `operator` varchar(64) DEFAULT '' COMMENT '运营商',
  `idfa` varchar(64) DEFAULT '' COMMENT 'iphone的IDFA',
  `idfv` varchar(255) DEFAULT '' COMMENT 'iphone的IDFV',
  `open_udid` varchar(255) DEFAULT '' COMMENT 'iphone的OpenUdid',
  `ip` varchar(32) DEFAULT '' COMMENT 'IP地址',
  `wlan_ip` varchar(64) DEFAULT '' COMMENT '局网ip地址',
  `user_agent` varchar(255) DEFAULT '' COMMENT '浏览器的UA',
  `time` datetime DEFAULT NULL COMMENT '客户端时间',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(11) DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(11) DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_风控日志';

-- --------------------------------------------------------

--
-- 表的结构 `common_theme`
--

CREATE TABLE `common_theme` (
  `id` int(11) NOT NULL COMMENT '主键',
  `merchant_id` int(11) NOT NULL DEFAULT '0' COMMENT '商户ID',
  `member_id` int(11) DEFAULT '0' COMMENT '用户ID',
  `member_type` int(11) DEFAULT '0' COMMENT '用户类型',
  `app_id` varchar(20) NOT NULL DEFAULT '' COMMENT '应用',
  `layout` varchar(50) DEFAULT NULL COMMENT '布局类型',
  `color` varchar(50) DEFAULT 'black' COMMENT '主题颜色',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '添加时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_用户主题';

-- --------------------------------------------------------

--
-- 表的结构 `dict`
--

CREATE TABLE `dict` (
  `id` int(11) NOT NULL COMMENT 'Id',
  `code` varchar(255) NOT NULL COMMENT '字典编码',
  `name` varchar(255) NOT NULL COMMENT '字典名称',
  `remark` text NOT NULL COMMENT '字典用途',
  `merchant_id` int(11) DEFAULT '0' COMMENT '租户',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典信息表';

-- --------------------------------------------------------

--
-- 表的结构 `dict_item`
--

CREATE TABLE `dict_item` (
  `id` bigint(20) NOT NULL COMMENT 'Id',
  `dict_id` int(11) DEFAULT '0' COMMENT '字典Id',
  `text` varchar(255) NOT NULL COMMENT '选项名称',
  `value` varchar(255) NOT NULL COMMENT '选项值',
  `pid` bigint(20) NOT NULL DEFAULT '0' COMMENT '上级选项',
  `is_default` int(11) DEFAULT '0' COMMENT '是否默认选中',
  `remark` varchar(255) DEFAULT NULL COMMENT '用途描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `level` int(11) DEFAULT '0' COMMENT '层级',
  `tree` varchar(255) DEFAULT NULL COMMENT '结构树',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间',
  `color` varchar(255) DEFAULT NULL COMMENT '约定表现色'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典清单表';

-- --------------------------------------------------------

--
-- 表的结构 `download_task`
--

CREATE TABLE `download_task` (
  `id` int(11) NOT NULL,
  `merchant_id` int(11) DEFAULT NULL COMMENT '租户',
  `user_id` int(11) NOT NULL COMMENT '所属会员',
  `target_model` varchar(128) NOT NULL COMMENT '模型',
  `model_name` varchar(128) NOT NULL COMMENT '模型名称',
  `search_json` text COMMENT '下载条件',
  `download_url` varchar(255) DEFAULT NULL COMMENT '下载地址',
  `state` tinyint(4) DEFAULT '0' COMMENT '任务状态',
  `status` tinyint(4) DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间',
  `queue_id` varchar(128) DEFAULT NULL COMMENT '排队编码'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='下载任务表';

-- --------------------------------------------------------

--
-- 表的结构 `extend_config`
--

CREATE TABLE `extend_config` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户',
  `store_id` int(11) DEFAULT '0' COMMENT '门店',
  `title` varchar(50) DEFAULT '' COMMENT '配置标题',
  `name` varchar(50) DEFAULT '' COMMENT '配置标识',
  `type` varchar(30) DEFAULT '' COMMENT '配置类型',
  `remark` varchar(1000) DEFAULT '' COMMENT '说明',
  `data` json DEFAULT NULL COMMENT '配置',
  `sort` int(10) UNSIGNED DEFAULT '0' COMMENT '排序',
  `extend` int(11) DEFAULT '0' COMMENT '扩展字段',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扩展_配置表';

-- --------------------------------------------------------

--
-- 表的结构 `extend_pay_log`
--

CREATE TABLE `extend_pay_log` (
  `id` int(11) NOT NULL COMMENT '主键',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `app_id` varchar(50) DEFAULT '' COMMENT '应用id',
  `out_trade_no` varchar(32) DEFAULT '' COMMENT '商户订单号',
  `order_sn` varchar(30) DEFAULT '' COMMENT '关联订单号',
  `order_group` varchar(20) DEFAULT '' COMMENT '组别[默认统一支付类型]',
  `openid` varchar(50) DEFAULT '' COMMENT 'openid',
  `mch_id` varchar(20) DEFAULT '' COMMENT '商户支付账户',
  `body` varchar(100) DEFAULT '' COMMENT '支付内容',
  `detail` varchar(100) DEFAULT '' COMMENT '支付详情',
  `auth_code` varchar(50) DEFAULT '' COMMENT '刷卡码',
  `transaction_id` varchar(50) DEFAULT '' COMMENT '关联订单号',
  `total_fee` decimal(10,2) DEFAULT '0.00' COMMENT '初始金额',
  `fee_type` varchar(20) DEFAULT '' COMMENT '标价币种',
  `pay_type` int(11) NOT NULL DEFAULT '0' COMMENT '支付类型',
  `pay_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `pay_status` tinyint(4) DEFAULT '0' COMMENT '支付状态',
  `pay_time` int(11) DEFAULT '0' COMMENT '支付时间',
  `trade_type` varchar(16) DEFAULT '' COMMENT '交易类型',
  `refund_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
  `refund_type` tinyint(4) DEFAULT '0' COMMENT '退款情况[0:未退款;1部分退款;2:全部退款]',
  `create_ip` varchar(30) DEFAULT '' COMMENT '创建者ip',
  `pay_ip` varchar(30) DEFAULT '' COMMENT '支付者ip',
  `unite_no` varchar(30) DEFAULT '' COMMENT '联合订单号',
  `notify_url` varchar(100) DEFAULT '' COMMENT '支付通知回调地址',
  `return_url` varchar(100) DEFAULT '' COMMENT '买家付款成功跳转地址',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `is_error` tinyint(4) DEFAULT '0' COMMENT '是否有报错',
  `error_data` json DEFAULT NULL COMMENT '报错日志',
  `req_id` varchar(50) DEFAULT '' COMMENT '对外id',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(11) DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(11) DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扩展_支付日志';

-- --------------------------------------------------------

--
-- 表的结构 `extend_pay_refund`
--

CREATE TABLE `extend_pay_refund` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键id',
  `pay_id` int(11) DEFAULT '0' COMMENT '支付ID',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(11) DEFAULT '0' COMMENT '买家id',
  `app_id` varchar(50) DEFAULT '' COMMENT '应用id',
  `order_sn` varchar(30) DEFAULT '' COMMENT '关联订单号',
  `order_group` varchar(20) DEFAULT '' COMMENT '组别[默认统一支付类型]',
  `out_trade_no` varchar(32) DEFAULT '' COMMENT '商户订单号',
  `transaction_id` varchar(50) DEFAULT '' COMMENT '微信订单号',
  `refund_trade_no` varchar(55) DEFAULT '' COMMENT '退款交易号',
  `refund_money` decimal(10,2) DEFAULT NULL COMMENT '退款金额',
  `refund_way` int(11) DEFAULT '0' COMMENT '退款方式',
  `ip` varchar(30) DEFAULT '' COMMENT '申请者ip',
  `error_data` json DEFAULT NULL COMMENT '报错日志',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `req_id` varchar(50) DEFAULT '' COMMENT '对外id',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(11) DEFAULT '0',
  `updated_at` int(11) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扩展_支付退款记录';

-- --------------------------------------------------------

--
-- 表的结构 `extend_sms_log`
--

CREATE TABLE `extend_sms_log` (
  `id` int(11) NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `mobile` varchar(20) DEFAULT '' COMMENT '手机号码',
  `code` varchar(6) DEFAULT '' COMMENT '验证码',
  `content` varchar(500) DEFAULT '' COMMENT '内容',
  `error_code` int(11) DEFAULT '0' COMMENT '报错code',
  `error_msg` varchar(200) DEFAULT '' COMMENT '报错信息',
  `error_data` longtext COMMENT '报错日志',
  `usage` varchar(20) DEFAULT '' COMMENT '用途',
  `used` tinyint(1) DEFAULT '0' COMMENT '是否使用[0:未使用;1:已使用]',
  `use_time` int(11) DEFAULT '0' COMMENT '使用时间',
  `ip` varchar(30) DEFAULT '' COMMENT 'ip地址',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `req_id` varchar(50) DEFAULT '' COMMENT '对外id',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(-1:已删除,0:禁用,1:正常)',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扩展_短信发送日志';

-- --------------------------------------------------------

--
-- 表的结构 `hccj`
--

CREATE TABLE `hccj` (
  `id` int(11) NOT NULL,
  `merchant_id` int(11) DEFAULT '0' COMMENT '租户',
  `begin_date` date NOT NULL COMMENT '调度开始日期',
  `end_date` date NOT NULL COMMENT '调度结束日期',
  `name` varchar(255) NOT NULL COMMENT '调度名称',
  `state` tinyint(2) DEFAULT '0' COMMENT '调度状态',
  `up_date` date DEFAULT NULL COMMENT '上报日期',
  `up_state` tinyint(2) DEFAULT '0' COMMENT '上报状态',
  `status` tinyint(2) DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间',
  `file_1` varchar(255) DEFAULT NULL COMMENT '专项整治进度表',
  `file_2` varchar(255) DEFAULT NULL COMMENT '工程质量问题—区域统计',
  `file_3` varchar(255) DEFAULT NULL COMMENT '工程质量问题具体情况—区域统计',
  `file_4` varchar(255) DEFAULT NULL COMMENT '项目管理问题—区域统计',
  `file_5` varchar(255) DEFAULT NULL COMMENT '项目清单',
  `region_id` int(11) DEFAULT '0' COMMENT '所属地区'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调度表';

-- --------------------------------------------------------

--
-- 表的结构 `member`
--

CREATE TABLE `member` (
  `id` int(11) NOT NULL,
  `merchant_id` int(11) DEFAULT '0' COMMENT '商户ID',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `username` varchar(20) NOT NULL DEFAULT '' COMMENT '账号',
  `password_hash` varchar(150) NOT NULL DEFAULT '' COMMENT '密码',
  `auth_key` varchar(32) NOT NULL DEFAULT '' COMMENT '授权令牌',
  `password_reset_token` varchar(150) DEFAULT '' COMMENT '密码重置令牌',
  `mobile_reset_token` varchar(150) DEFAULT '' COMMENT '手机号码重置令牌',
  `type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `realname` varchar(50) DEFAULT '' COMMENT '真实姓名',
  `nickname` varchar(60) DEFAULT '' COMMENT '昵称',
  `head_portrait` char(150) DEFAULT '' COMMENT '头像',
  `gender` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '性别[0:未知;1:男;2:女]',
  `qq` varchar(20) DEFAULT '' COMMENT 'qq',
  `email` varchar(60) DEFAULT '' COMMENT '邮箱',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `province_id` int(11) DEFAULT '0' COMMENT '省',
  `city_id` int(11) DEFAULT '0' COMMENT '城市',
  `area_id` int(11) DEFAULT '0' COMMENT '地区',
  `address` varchar(100) DEFAULT '' COMMENT '默认地址',
  `mobile` varchar(20) DEFAULT '' COMMENT '手机号码',
  `tel_no` varchar(20) DEFAULT '' COMMENT '电话号码',
  `bg_image` varchar(200) DEFAULT '' COMMENT '个人背景图',
  `description` varchar(200) DEFAULT '' COMMENT '个人说明',
  `visit_count` smallint(5) UNSIGNED DEFAULT '0' COMMENT '访问次数',
  `last_time` int(11) DEFAULT '0' COMMENT '最后一次登录时间',
  `last_ip` varchar(40) DEFAULT '' COMMENT '最后一次登录ip',
  `role` smallint(6) DEFAULT '10' COMMENT '权限',
  `current_level` tinyint(4) DEFAULT '1' COMMENT '当前级别',
  `level_expiration_time` int(11) DEFAULT '0' COMMENT '等级到期时间',
  `level_buy_type` tinyint(4) DEFAULT '1' COMMENT '1:赠送;2:购买',
  `pid` int(10) UNSIGNED DEFAULT '0' COMMENT '上级id',
  `level` tinyint(3) UNSIGNED DEFAULT '1' COMMENT '级别',
  `tree` varchar(2000) DEFAULT '' COMMENT '树',
  `promoter_code` varchar(50) DEFAULT '' COMMENT '推广码',
  `certification_type` tinyint(4) DEFAULT '0' COMMENT '认证类型',
  `source` varchar(50) DEFAULT '' COMMENT '注册来源',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间',
  `region_id` int(11) DEFAULT '0' COMMENT '数据权限'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员表';

-- --------------------------------------------------------

--
-- 表的结构 `member_account`
--

CREATE TABLE `member_account` (
  `id` int(10) UNSIGNED NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `user_money` decimal(10,2) DEFAULT '0.00' COMMENT '当前余额',
  `accumulate_money` decimal(10,2) DEFAULT '0.00' COMMENT '累计余额',
  `give_money` decimal(10,2) DEFAULT '0.00' COMMENT '累计赠送余额',
  `consume_money` decimal(10,2) DEFAULT '0.00' COMMENT '累计消费金额',
  `frozen_money` decimal(10,2) DEFAULT '0.00' COMMENT '冻结金额',
  `user_integral` int(11) DEFAULT '0' COMMENT '当前积分',
  `accumulate_integral` int(11) DEFAULT '0' COMMENT '累计积分',
  `give_integral` int(11) DEFAULT '0' COMMENT '累计赠送积分',
  `consume_integral` decimal(10,2) DEFAULT '0.00' COMMENT '累计消费积分',
  `frozen_integral` int(11) DEFAULT '0' COMMENT '冻结积分',
  `user_growth` int(11) DEFAULT '0' COMMENT '当前成长值',
  `accumulate_growth` int(11) DEFAULT '0' COMMENT '累计成长值',
  `consume_growth` int(11) DEFAULT '0' COMMENT '累计消费成长值',
  `frozen_growth` int(11) DEFAULT '0' COMMENT '冻结成长值',
  `economize_money` decimal(10,2) DEFAULT '0.00' COMMENT '已节约金额',
  `accumulate_drawn_money` decimal(10,2) DEFAULT '0.00' COMMENT '累计提现',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_账户表';

-- --------------------------------------------------------

--
-- 表的结构 `member_address`
--

CREATE TABLE `member_address` (
  `id` int(11) NOT NULL COMMENT '主键',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `realname` varchar(100) DEFAULT '' COMMENT '真实姓名',
  `mobile` varchar(20) DEFAULT '' COMMENT '手机号码',
  `province_id` int(10) UNSIGNED DEFAULT '0' COMMENT '省',
  `city_id` int(10) UNSIGNED DEFAULT '0' COMMENT '市',
  `area_id` int(10) UNSIGNED DEFAULT '0' COMMENT '区',
  `name` varchar(200) DEFAULT '' COMMENT '省市区名称',
  `details` varchar(200) DEFAULT '' COMMENT '详细地址',
  `street_number` varchar(200) DEFAULT '' COMMENT '门牌号',
  `longitude` varchar(100) DEFAULT '' COMMENT '经度',
  `latitude` varchar(100) DEFAULT '' COMMENT '纬度',
  `floor_level` tinyint(4) DEFAULT '0' COMMENT '楼层',
  `zip_code` varchar(10) DEFAULT '' COMMENT '邮编',
  `tel_no` varchar(20) DEFAULT '' COMMENT '家庭号码',
  `is_default` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '默认地址',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(-1:已删除,0:禁用,1:正常)',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_收货地址表';

-- --------------------------------------------------------

--
-- 表的结构 `member_auth`
--

CREATE TABLE `member_auth` (
  `id` int(10) UNSIGNED NOT NULL COMMENT '主键',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `unionid` varchar(64) DEFAULT '' COMMENT '唯一ID',
  `oauth_client` varchar(20) DEFAULT '' COMMENT '授权组别',
  `oauth_client_user_id` varchar(100) DEFAULT '' COMMENT '授权id',
  `gender` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '性别[0:未知;1:男;2:女]',
  `nickname` varchar(100) DEFAULT '' COMMENT '昵称',
  `head_portrait` varchar(200) DEFAULT '' COMMENT '头像',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `country` varchar(100) DEFAULT '' COMMENT '国家',
  `province` varchar(100) DEFAULT '' COMMENT '省',
  `city` varchar(100) DEFAULT '' COMMENT '市',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(-1:已删除,0:禁用,1:正常)',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_第三方授权';

-- --------------------------------------------------------

--
-- 表的结构 `member_bank_account`
--

CREATE TABLE `member_bank_account` (
  `id` int(11) NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(11) DEFAULT '0' COMMENT '会员id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `realname` varchar(50) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `account_number` varchar(50) DEFAULT '' COMMENT '银行账号',
  `account_type` int(11) DEFAULT '10' COMMENT '账户类型',
  `account_type_name` varchar(30) DEFAULT '' COMMENT '账户类型名称',
  `bank_name` varchar(100) DEFAULT '' COMMENT '银行信息',
  `bank_branch` varchar(200) DEFAULT '' COMMENT '银行支行信息',
  `identity_card` varchar(20) DEFAULT '' COMMENT '身份证',
  `identity_card_front` varchar(200) DEFAULT '' COMMENT '身份证正面',
  `identity_card_back` varchar(200) DEFAULT '' COMMENT '身份证背面',
  `is_default` tinyint(4) DEFAULT '0' COMMENT '是否默认账号',
  `audit_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '审核状态[0:申请;1通过;-1失败]',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_提现账号';

-- --------------------------------------------------------

--
-- 表的结构 `member_cancel`
--

CREATE TABLE `member_cancel` (
  `id` int(10) UNSIGNED NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '会员id',
  `content` text COMMENT '申请内容',
  `audit_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '审核状态[0:申请;1通过;-1失败]',
  `audit_time` int(10) UNSIGNED DEFAULT '0' COMMENT '审核时间',
  `refusal_cause` varchar(200) DEFAULT '' COMMENT '拒绝原因',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态',
  `created_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_注销申请';

-- --------------------------------------------------------

--
-- 表的结构 `member_certification`
--

CREATE TABLE `member_certification` (
  `id` int(10) UNSIGNED NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '用户类型',
  `realname` varchar(100) DEFAULT '' COMMENT '真实姓名',
  `identity_card` varchar(50) DEFAULT '' COMMENT '身份证号码',
  `identity_card_front` varchar(200) DEFAULT '' COMMENT '身份证国徽面',
  `identity_card_back` varchar(200) DEFAULT '' COMMENT '身份证人像面',
  `gender` varchar(10) DEFAULT '' COMMENT '性别',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `front_is_fake` tinyint(4) DEFAULT '0' COMMENT '正面是否是复印件',
  `back_is_fake` tinyint(4) DEFAULT '0' COMMENT '背面是否是复印件',
  `nationality` varchar(100) DEFAULT '' COMMENT '民族 ',
  `address` varchar(255) DEFAULT '' COMMENT '地址',
  `start_date` date DEFAULT NULL COMMENT '有效期起始时间',
  `end_date` date DEFAULT NULL COMMENT '有效期结束时间',
  `issue` varchar(200) DEFAULT '' COMMENT '签发机关 ',
  `is_self` tinyint(4) DEFAULT '0' COMMENT '自己认证',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_实名认证';

-- --------------------------------------------------------

--
-- 表的结构 `member_credits_log`
--

CREATE TABLE `member_credits_log` (
  `id` int(11) NOT NULL,
  `app_id` varchar(50) DEFAULT '' COMMENT '应用id',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `pay_type` tinyint(4) DEFAULT '0' COMMENT '支付类型',
  `type` varchar(50) NOT NULL DEFAULT '' COMMENT '变动类型[integral:积分;money:余额]',
  `group` varchar(50) DEFAULT '' COMMENT '变动的组别',
  `old_num` decimal(10,2) DEFAULT '0.00' COMMENT '之前的数据',
  `new_num` decimal(10,2) DEFAULT '0.00' COMMENT '变动后的数据',
  `num` decimal(10,2) DEFAULT '0.00' COMMENT '变动的数据',
  `remark` varchar(200) DEFAULT '' COMMENT '备注',
  `ip` varchar(50) DEFAULT '' COMMENT 'ip地址',
  `map_id` int(11) DEFAULT '0' COMMENT '关联id',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_积分等变动表';

-- --------------------------------------------------------

--
-- 表的结构 `member_invoice`
--

CREATE TABLE `member_invoice` (
  `id` int(10) UNSIGNED NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `title` varchar(200) DEFAULT '' COMMENT '公司抬头',
  `duty_paragraph` varchar(200) DEFAULT '' COMMENT '公司税号',
  `opening_bank` varchar(255) DEFAULT '' COMMENT '公司开户行',
  `opening_bank_account` varchar(100) DEFAULT '' COMMENT '公司开户行账号',
  `address` varchar(255) DEFAULT '' COMMENT '公司地址',
  `phone` varchar(50) DEFAULT '' COMMENT '公司电话',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `is_default` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '默认',
  `type` tinyint(4) DEFAULT '1' COMMENT '类型 1企业 2个人',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(-1:已删除,0:禁用,1:正常)',
  `created_at` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_发票';

-- --------------------------------------------------------

--
-- 表的结构 `member_level`
--

CREATE TABLE `member_level` (
  `id` int(11) NOT NULL COMMENT '主键',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `level` int(11) DEFAULT '0' COMMENT '等级（数字越大等级越高）',
  `name` varchar(255) DEFAULT '' COMMENT '等级名称',
  `icon` varchar(255) DEFAULT '' COMMENT '等级图标',
  `cover` varchar(255) DEFAULT '' COMMENT '等级背景图',
  `detail` varchar(255) DEFAULT '' COMMENT '等级介绍',
  `money` decimal(10,2) DEFAULT '0.00' COMMENT '消费额度满足则升级',
  `integral` int(11) DEFAULT '0' COMMENT '消费积分满足则升级',
  `growth` int(11) DEFAULT '0' COMMENT '成长值满足则升级',
  `discount` decimal(10,2) DEFAULT '10.00' COMMENT '折扣',
  `status` int(11) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_等级表';

-- --------------------------------------------------------

--
-- 表的结构 `member_level_config`
--

CREATE TABLE `member_level_config` (
  `id` int(11) NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `upgrade_type` tinyint(4) DEFAULT '1' COMMENT '升级方式',
  `auto_upgrade_type` tinyint(4) DEFAULT '1' COMMENT '自动升级类型',
  `status` int(11) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_等级配置';

-- --------------------------------------------------------

--
-- 表的结构 `member_stat`
--

CREATE TABLE `member_stat` (
  `id` int(11) NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺id',
  `member_id` int(11) DEFAULT NULL COMMENT '用户id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `nice_num` int(11) DEFAULT '0' COMMENT '点赞数量',
  `disagree_num` int(11) DEFAULT '0' COMMENT '不赞同数量',
  `transmit_num` int(11) DEFAULT '0' COMMENT '转发数量',
  `comment_num` int(11) DEFAULT '0' COMMENT '评论数量',
  `collect_num` int(11) DEFAULT '0' COMMENT '收藏',
  `report_num` int(11) DEFAULT '0' COMMENT '举报数量',
  `recommend_num` int(11) DEFAULT '0' COMMENT '推荐数量',
  `follow_num` int(11) DEFAULT '0' COMMENT '关注人数',
  `allowed_num` int(11) DEFAULT '0' COMMENT '被关注人数',
  `view` int(11) DEFAULT '0' COMMENT '浏览量',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='食谱_作品';

-- --------------------------------------------------------

--
-- 表的结构 `member_tag`
--

CREATE TABLE `member_tag` (
  `id` int(11) NOT NULL COMMENT '主键',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态',
  `created_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_标签表';

-- --------------------------------------------------------

--
-- 表的结构 `member_tag_map`
--

CREATE TABLE `member_tag_map` (
  `tag_id` int(11) DEFAULT '0' COMMENT '标签id',
  `member_id` int(11) DEFAULT '0' COMMENT '文章id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_标签关联表';

-- --------------------------------------------------------

--
-- 表的结构 `member_withdraw_deposit`
--

CREATE TABLE `member_withdraw_deposit` (
  `id` int(11) NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(11) DEFAULT NULL COMMENT '会员id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `withdraw_no` varchar(100) DEFAULT '' COMMENT '提现流水号',
  `batch_no` varchar(100) DEFAULT '' COMMENT '批量转账单号',
  `realname` varchar(50) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `account_number` varchar(50) DEFAULT '' COMMENT '银行账号',
  `account_type` int(11) DEFAULT '1' COMMENT '账户类型',
  `account_type_name` varchar(30) DEFAULT '' COMMENT '账户类型名称',
  `bank_name` varchar(100) DEFAULT '' COMMENT '银行信息',
  `bank_branch` varchar(200) DEFAULT '' COMMENT '银行支行信息',
  `identity_card` varchar(20) DEFAULT '' COMMENT '身份证',
  `identity_card_front` varchar(200) DEFAULT '' COMMENT '身份证正面',
  `identity_card_back` varchar(200) DEFAULT '' COMMENT '身份证背面',
  `cash` decimal(10,2) DEFAULT '0.00' COMMENT '提现金额',
  `memo` varchar(200) DEFAULT '' COMMENT '备注',
  `transfer_type` int(11) DEFAULT '1' COMMENT '转账方式',
  `transfer_name` varchar(50) DEFAULT '' COMMENT '转账银行名称',
  `transfer_money` decimal(10,2) DEFAULT '0.00' COMMENT '转账金额',
  `transfer_remark` varchar(200) DEFAULT '' COMMENT '转账备注',
  `transfer_no` varchar(100) DEFAULT '' COMMENT '转账流水号',
  `transfer_account_no` varchar(100) DEFAULT '' COMMENT '转账银行账号',
  `transfer_result` varchar(200) DEFAULT '' COMMENT '转账结果',
  `transfer_time` int(11) DEFAULT '0' COMMENT '转账时间',
  `transfer_status` int(11) DEFAULT '0' COMMENT '转账状态',
  `payment_time` int(11) DEFAULT '0' COMMENT '到账时间',
  `audit_time` int(10) UNSIGNED DEFAULT '0' COMMENT '审核时间',
  `service_charge` decimal(10,2) DEFAULT '0.00' COMMENT '手续费率金额',
  `service_charge_rate` decimal(10,2) DEFAULT NULL COMMENT '手续费率',
  `service_charge_single` decimal(10,2) DEFAULT '0.00' COMMENT '手续费单笔',
  `service_charge_total` decimal(10,2) DEFAULT '0.00' COMMENT '总手续费',
  `refusal_cause` varchar(200) DEFAULT '' COMMENT '拒绝原因',
  `notify_url` varchar(255) DEFAULT '' COMMENT '通知地址',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_提现记录表';

-- --------------------------------------------------------

--
-- 表的结构 `merchant`
--

CREATE TABLE `merchant` (
  `id` int(10) UNSIGNED NOT NULL,
  `title` varchar(200) DEFAULT '' COMMENT '商户名称',
  `cover` char(150) DEFAULT '' COMMENT '店铺头像',
  `address_name` varchar(200) DEFAULT '' COMMENT '地址',
  `address_details` varchar(100) DEFAULT '' COMMENT '详细地址',
  `longitude` varchar(100) DEFAULT '' COMMENT '经度',
  `latitude` varchar(100) DEFAULT '' COMMENT '纬度',
  `collect_num` int(10) UNSIGNED DEFAULT '0' COMMENT '收藏数量',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家';

-- --------------------------------------------------------

--
-- 表的结构 `migration`
--

CREATE TABLE `migration` (
  `version` varchar(180) NOT NULL,
  `apply_time` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `mp_list`
--

CREATE TABLE `mp_list` (
  `id` bigint(20) NOT NULL COMMENT 'Id',
  `region_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属地区',
  `dm` int(11) DEFAULT NULL COMMENT '区划代码',
  `project_id` bigint(20) NOT NULL COMMENT '所属项目',
  `xmczzywt` int(11) DEFAULT NULL COMMENT '项目存在主要问题',
  `csxmmj` decimal(10,3) DEFAULT '0.000' COMMENT '缺失项目面积（万亩）',
  `is_zg` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已整改',
  `is_up_2025` tinyint(4) NOT NULL DEFAULT '0' COMMENT '拟列入2025年改造提升项目',
  `is_up_2627` tinyint(4) NOT NULL DEFAULT '0' COMMENT '拟列2026-2027年改造提升项目',
  `is_up_2830` tinyint(4) NOT NULL DEFAULT '0' COMMENT '拟列2028-2030年改造提升项目',
  `is_sj` tinyint(4) NOT NULL DEFAULT '0' COMMENT '审计是否发现问题',
  `sj_rematk` text COMMENT '审核发现问题内容',
  `zg_remark` text COMMENT '整改方案',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模排清单表';

-- --------------------------------------------------------

--
-- 表的结构 `mp_live`
--

CREATE TABLE `mp_live` (
  `id` int(11) NOT NULL COMMENT 'Id',
  `merchant_id` int(11) DEFAULT '0' COMMENT '租户',
  `region_id` int(11) NOT NULL COMMENT '所属地区',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `images` text COMMENT '现场照片',
  `boss_type` varchar(80) DEFAULT NULL COMMENT '领导类型',
  `content` text COMMENT '描述',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间',
  `projects` text COMMENT '关联项目'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='现场模排表';

-- --------------------------------------------------------

--
-- 表的结构 `mp_question`
--

CREATE TABLE `mp_question` (
  `id` int(11) NOT NULL,
  `merchant_id` int(11) DEFAULT '0' COMMENT '租户',
  `type` tinyint(4) DEFAULT '0' COMMENT '类型',
  `project_id` int(11) NOT NULL DEFAULT '0' COMMENT '归属项目',
  `tjsl` text COMMENT '田间水利工程设施摸查总体情况',
  `other` text COMMENT '各类田间水利工程设施问题N情况',
  `p_total` int(11) DEFAULT '0' COMMENT '项目设计与监理问题总数',
  `p1` int(11) DEFAULT '0' COMMENT '1.勘察设计不到现场、凭空设计',
  `p2` int(11) DEFAULT '0' COMMENT '2.未广泛征求和吸收项目区群众、村级组织等意见',
  `p3` int(11) DEFAULT '0' COMMENT '3.缺少总体设计图',
  `p4` int(11) DEFAULT '0' COMMENT '4.缺少重要单体工程图',
  `p5` int(11) DEFAULT '0' COMMENT '5.监理日志造假、监理签名代签',
  `p6` int(11) DEFAULT '0' COMMENT '6.缺少工程设施具体分布图',
  `p7` int(11) DEFAULT '0' COMMENT '未展开工程质量平行检验、未7.按规定对隐蔽工程开展旁站巡视',
  `p8` int(11) DEFAULT '0' COMMENT '8.监理单位与建设单位串通',
  `status` tinyint(4) DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间',
  `mj_total` decimal(10,2) DEFAULT '0.00' COMMENT '耕作田块面积',
  `no_total` decimal(10,2) DEFAULT '0.00' COMMENT '抛荒面积',
  `go_zg` varchar(255) DEFAULT NULL COMMENT '可立行立改',
  `jlwt` text,
  `xjwt` text,
  `file_1` varchar(255) DEFAULT NULL COMMENT '文件',
  `file_2` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='摸排问题填报表';

-- --------------------------------------------------------

--
-- 表的结构 `oauth2_access_token`
--

CREATE TABLE `oauth2_access_token` (
  `id` int(10) UNSIGNED NOT NULL,
  `access_token` varchar(80) NOT NULL COMMENT '授权Token',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `auth_key` varchar(32) DEFAULT '' COMMENT '授权令牌',
  `client_id` varchar(64) NOT NULL COMMENT '授权ID',
  `member_id` varchar(100) DEFAULT '' COMMENT '用户ID',
  `expires` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '有效期',
  `scope` json DEFAULT NULL COMMENT '授权权限',
  `grant_type` varchar(30) DEFAULT '' COMMENT '组别',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='oauth2_授权令牌';

-- --------------------------------------------------------

--
-- 表的结构 `oauth2_authorization_code`
--

CREATE TABLE `oauth2_authorization_code` (
  `authorization_code` varchar(100) NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `client_id` varchar(64) NOT NULL COMMENT '授权ID',
  `member_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
  `redirect_uri` varchar(2000) DEFAULT NULL COMMENT '回调url',
  `expires` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '有效期',
  `scope` json DEFAULT NULL COMMENT '授权权限',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='oauth2_授权回调code';

-- --------------------------------------------------------

--
-- 表的结构 `oauth2_client`
--

CREATE TABLE `oauth2_client` (
  `id` int(10) UNSIGNED NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
  `client_id` varchar(64) NOT NULL COMMENT '授权ID',
  `client_secret` varchar(64) NOT NULL COMMENT '授权秘钥',
  `redirect_uri` varchar(2000) DEFAULT '' COMMENT '回调Url',
  `remark` varchar(200) DEFAULT '' COMMENT '备注',
  `group` varchar(30) DEFAULT '' COMMENT '组别',
  `scope` json DEFAULT NULL COMMENT '授权',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='oauth2_授权客户端';

-- --------------------------------------------------------

--
-- 表的结构 `oauth2_refresh_token`
--

CREATE TABLE `oauth2_refresh_token` (
  `refresh_token` varchar(80) NOT NULL,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `client_id` varchar(64) NOT NULL COMMENT '授权ID',
  `member_id` varchar(100) DEFAULT '' COMMENT '用户ID',
  `expires` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '有效期',
  `scope` json DEFAULT NULL COMMENT '授权权限',
  `grant_type` varchar(30) DEFAULT '' COMMENT '组别',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='oauth2_授权令牌';

-- --------------------------------------------------------

--
-- 表的结构 `project`
--

CREATE TABLE `project` (
  `id` bigint(20) NOT NULL COMMENT 'Id',
  `sn` varchar(255) DEFAULT NULL COMMENT '自定义编码',
  `xmdm` varchar(255) DEFAULT NULL COMMENT '项目代码',
  `xmbm` varchar(255) DEFAULT NULL COMMENT '项目编号',
  `xmmc` varchar(255) NOT NULL COMMENT '项目名称',
  `nf` int(11) NOT NULL COMMENT '立项年度',
  `region_id` int(11) NOT NULL COMMENT '地区Id',
  `xzqhmc` varchar(255) DEFAULT NULL COMMENT '行政区划名称',
  `xzqhdm` varchar(255) DEFAULT NULL COMMENT '行政区划代码',
  `createTime` varchar(255) DEFAULT NULL COMMENT '创建时间',
  `xmtype` varchar(255) DEFAULT NULL COMMENT '项目类型',
  `xmtype_dictText` varchar(255) DEFAULT NULL COMMENT '项目类型名称',
  `xmzt` varchar(255) DEFAULT NULL COMMENT '项目状态',
  `ghmj` decimal(10,4) DEFAULT NULL COMMENT '建设面积（亩）',
  `xzjsmj` bigint(20) DEFAULT '0' COMMENT '新增建设面积（亩）',
  `gztsmj` int(11) DEFAULT '0' COMMENT '改造提升面积（亩）',
  `gxjsmj` int(11) DEFAULT '0' COMMENT '新增高效节水灌溉面积（亩）',
  `jsdd` text COMMENT '建设地址',
  `jsnr` text COMMENT '建设内容',
  `bz` text COMMENT '备注',
  `czzj_zy` int(11) DEFAULT '0' COMMENT '中央投入（万元）',
  `czzj_tj` int(11) DEFAULT '0' COMMENT '省级投入（万元）',
  `czzj_sj` int(11) DEFAULT '0' COMMENT '市级投入（万元）',
  `czzj_xj` int(11) DEFAULT '0' COMMENT '其它投入（万元）',
  `ztz` decimal(11,2) DEFAULT '0.00' COMMENT '投资估算（万元）',
  `xmgdzlLevel` decimal(10,2) DEFAULT '0.00' COMMENT '项目区耕地质量等级',
  `dxdm` varchar(255) DEFAULT NULL COMMENT '地形地貌',
  `dddcts` int(11) DEFAULT '0' COMMENT '大豆单产提升',
  `ymdcts` int(11) DEFAULT '0' COMMENT '玉米单产提升',
  `ylscjd` int(11) DEFAULT '0' COMMENT '油料生产基地',
  `mhjd` int(11) DEFAULT '0' COMMENT '棉花生产基地',
  `ddscjd` int(11) DEFAULT '0' COMMENT '大豆生产基地',
  `json` text COMMENT '原始json值',
  `merchant_id` int(11) DEFAULT '0' COMMENT '租户',
  `status` tinyint(4) DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间',
  `sldmpcs` int(11) DEFAULT '0' COMMENT '市级党委、政府领导 摸排、调研次数',
  `xldmpcs` int(11) DEFAULT '0' COMMENT '县级党委、政府领导 摸排、调研次数',
  `main` varchar(128) DEFAULT NULL COMMENT '项目负责人',
  `project_main` varchar(128) DEFAULT NULL COMMENT '项目负责人',
  `main_dep` varchar(128) DEFAULT NULL COMMENT '项目主管部门'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='农田项目基本信息表';

-- --------------------------------------------------------

--
-- 表的结构 `project_build`
--

CREATE TABLE `project_build` (
  `id` bigint(20) NOT NULL COMMENT 'Id',
  `merchant_id` int(11) DEFAULT '0' COMMENT '租户',
  `region_id` int(11) NOT NULL DEFAULT '0' COMMENT '指定地区',
  `project_id` int(11) DEFAULT '0' COMMENT '关联项目',
  `city` varchar(255) DEFAULT NULL COMMENT '市（州）',
  `area` varchar(255) DEFAULT NULL COMMENT '县（市，区）',
  `town` varchar(255) DEFAULT NULL COMMENT '乡镇（街道）',
  `xmmc` varchar(255) NOT NULL COMMENT '项目名称',
  `xmbh` varchar(255) NOT NULL COMMENT '项目编号',
  `build_unit` varchar(255) DEFAULT NULL COMMENT '建设单位',
  `jxdd` varchar(255) DEFAULT NULL COMMENT '建设地点',
  `nf` varchar(255) NOT NULL COMMENT '年度',
  `pifu_sn` varchar(255) DEFAULT NULL COMMENT '立项批复文号',
  `build_date` varchar(255) DEFAULT NULL COMMENT '立项时间',
  `pifu_money` decimal(10,2) DEFAULT NULL COMMENT '批复金额（元）',
  `plan_area` decimal(10,2) DEFAULT NULL COMMENT '拟建成高标准农田面积（万亩）',
  `plan_date` varchar(255) DEFAULT NULL COMMENT '计划完工时间',
  `jg_xmmc` varchar(255) DEFAULT NULL COMMENT '省级项目名称',
  `js_xmbh` varchar(255) DEFAULT NULL COMMENT '省级项目编号',
  `is_zb` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否招标',
  `cai_plan_beian` varchar(255) DEFAULT NULL COMMENT '采购计划备案号',
  `bao` varchar(255) DEFAULT NULL COMMENT '标段（包）',
  `xmmb` varchar(255) DEFAULT NULL COMMENT '项目类别',
  `zb_max_price` varchar(128) DEFAULT NULL COMMENT '招标控制价（元）',
  `zb_type` varchar(255) DEFAULT NULL COMMENT '招标方式',
  `zb_date` varchar(255) DEFAULT NULL COMMENT '招标时间',
  `is_qyzz` tinyint(4) NOT NULL DEFAULT '0' COMMENT '企业资质',
  `yq` varchar(255) DEFAULT NULL COMMENT '要求资质等级',
  `kb_date` varchar(255) DEFAULT NULL COMMENT '开标时间',
  `zbdws` int(11) DEFAULT '0' COMMENT '参与招投标单位数',
  `zb_company` varchar(255) DEFAULT NULL COMMENT '中标单位名称',
  `zb_company_sn` varchar(255) DEFAULT NULL COMMENT '中标单位统一社会信用代码',
  `zb_law_man` varchar(255) DEFAULT NULL COMMENT '中标单位法定代表人姓名',
  `zb_law_man_cadno` varchar(255) DEFAULT NULL COMMENT '中标单位法定代表人身份证号',
  `zb_money` varchar(128) DEFAULT NULL COMMENT '中标金额（元）',
  `zb_time` varchar(255) DEFAULT NULL COMMENT '中标时间',
  `zsmj` decimal(10,2) DEFAULT NULL COMMENT '高标准农田建设面积（亩）',
  `tjjzxxzdl` varchar(128) DEFAULT NULL COMMENT '田间基础设施占地率（%）',
  `nttthd` int(11) DEFAULT NULL COMMENT '农田土体厚度（cm）',
  `yxtchdldblshl` varchar(255) DEFAULT NULL COMMENT '有效土层厚度内地表砾石含量（粒径＞2mm）',
  `tdyjzhl` varchar(255) DEFAULT NULL COMMENT '土地有机质含量（g/kg）',
  `ggxjbzl` int(11) DEFAULT NULL COMMENT '灌溉设计保证率（%）',
  `dltdl` varchar(128) DEFAULT NULL COMMENT '道路通达度（%）',
  `qdktsj` varchar(255) DEFAULT NULL COMMENT '签订合同时间',
  `kt_total` varchar(128) DEFAULT NULL COMMENT '合同金额（元）',
  `yfgck` decimal(10,2) DEFAULT NULL COMMENT '预付工程款（元）',
  `kgsj` varchar(255) DEFAULT NULL COMMENT '开工时间',
  `ssdwmc` varchar(255) DEFAULT NULL COMMENT '实施单位名称',
  `ssdwm` varchar(255) DEFAULT NULL COMMENT '实施单位统一社会信用代码',
  `sswdfr` varchar(255) DEFAULT NULL COMMENT '实施单位法定代表人姓名',
  `ssdwfrsfz` varchar(255) DEFAULT NULL COMMENT '实施单位法定代表人身份证号',
  `xmjl` varchar(255) DEFAULT NULL COMMENT '项目经理姓名',
  `xmjlsfz` varchar(255) DEFAULT NULL COMMENT '项目经理身份证号',
  `zgyssj` varchar(255) DEFAULT NULL COMMENT '竣工验收时间',
  `zgjsj` varchar(128) DEFAULT NULL COMMENT '竣工决算价（元）',
  `jldwmc` varchar(255) DEFAULT NULL COMMENT '监理单位名称',
  `jldwm` varchar(255) DEFAULT NULL COMMENT '监理单位统一社会信用代码',
  `jldwfr` varchar(255) DEFAULT NULL COMMENT '监理单位法定代表人姓名',
  `jldwfrsfz` varchar(255) DEFAULT NULL COMMENT '监理单位法定代表人身份证号',
  `jlgcs` varchar(255) DEFAULT NULL COMMENT '监理工程师姓名',
  `jlgcssfz` varchar(255) DEFAULT NULL COMMENT '监理工程师身份证号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目建设信息表';

-- --------------------------------------------------------

--
-- 表的结构 `project_file`
--

CREATE TABLE `project_file` (
  `id` bigint(20) NOT NULL COMMENT 'Id',
  `merchant_id` int(11) DEFAULT '0' COMMENT '租户',
  `fjmc` varchar(255) NOT NULL COMMENT '文件名称',
  `fjdx` int(11) DEFAULT '0' COMMENT '附件大小',
  `fjid` varchar(255) DEFAULT NULL COMMENT '附件Id',
  `fjmx` varchar(255) DEFAULT NULL COMMENT '附件',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间',
  `createTime` varchar(255) DEFAULT NULL COMMENT '上传时间',
  `file_url` varchar(255) DEFAULT NULL COMMENT '文件地址',
  `ywid` varchar(255) DEFAULT NULL COMMENT '项目编码',
  `xmjd` varchar(255) DEFAULT NULL COMMENT '项目进度',
  `project_id` int(11) DEFAULT '0' COMMENT '项目Id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目附件表';

-- --------------------------------------------------------

--
-- 表的结构 `project_pifu`
--

CREATE TABLE `project_pifu` (
  `id` bigint(20) NOT NULL COMMENT 'Id',
  `region_id` int(11) NOT NULL DEFAULT '0' COMMENT '地区Id',
  `city` varchar(255) DEFAULT NULL COMMENT '市（州)',
  `area` varchar(255) DEFAULT NULL COMMENT '县（市、区）',
  `task_year` varchar(255) NOT NULL COMMENT '任务年度',
  `task_year_mou` decimal(10,2) DEFAULT NULL COMMENT '该年度任务数（亩）',
  `mou_avg_money` decimal(10,2) DEFAULT NULL COMMENT '亩均资金投入标准（元）',
  `xmmc` varchar(255) NOT NULL COMMENT '项目名称',
  `xmbh` varchar(255) NOT NULL COMMENT '项目编号',
  `pifu_sn` varchar(255) NOT NULL COMMENT '批复文号',
  `pifu_money` decimal(10,2) NOT NULL COMMENT '批复资金',
  `pifu_date` date DEFAULT NULL COMMENT '批复时间',
  `spt_xmmc` varchar(255) DEFAULT NULL COMMENT '所属省级项目名称',
  `spt_xmbh` varchar(255) DEFAULT NULL COMMENT '所属省级项目编号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间',
  `project_id` int(11) DEFAULT '0' COMMENT '关联项目'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目批复表';

-- --------------------------------------------------------

--
-- 表的结构 `rbac_auth_assignment`
--

CREATE TABLE `rbac_auth_assignment` (
  `role_id` int(11) NOT NULL COMMENT '角色',
  `user_id` int(11) NOT NULL COMMENT '用户',
  `app_id` varchar(20) DEFAULT '' COMMENT '应用入口'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_会员授权角色表';

-- --------------------------------------------------------

--
-- 表的结构 `rbac_auth_item`
--

CREATE TABLE `rbac_auth_item` (
  `id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL DEFAULT '' COMMENT '别名',
  `title` varchar(200) DEFAULT '' COMMENT '标题',
  `app_id` varchar(20) NOT NULL DEFAULT '' COMMENT '应用',
  `pid` int(11) DEFAULT '0' COMMENT '父级id',
  `level` int(11) DEFAULT '1' COMMENT '级别',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `sort` int(11) DEFAULT '9999' COMMENT '排序',
  `tree` varchar(500) DEFAULT '' COMMENT '树',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(11) DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(11) DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_权限表';

-- --------------------------------------------------------

--
-- 表的结构 `rbac_auth_item_child`
--

CREATE TABLE `rbac_auth_item_child` (
  `role_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id',
  `item_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '权限id',
  `name` varchar(64) NOT NULL DEFAULT '' COMMENT '别名',
  `app_id` varchar(20) NOT NULL DEFAULT '' COMMENT '类别',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_授权角色权限表';

-- --------------------------------------------------------

--
-- 表的结构 `rbac_auth_role`
--

CREATE TABLE `rbac_auth_role` (
  `id` int(11) NOT NULL COMMENT '主键',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题',
  `app_id` varchar(20) NOT NULL DEFAULT '' COMMENT '应用',
  `pid` int(10) UNSIGNED DEFAULT '0' COMMENT '上级id',
  `level` tinyint(3) UNSIGNED DEFAULT '1' COMMENT '级别',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `tree` varchar(300) NOT NULL DEFAULT '' COMMENT '树',
  `operating_type` tinyint(4) DEFAULT '2' COMMENT '运营类型',
  `annual_fee` decimal(10,2) DEFAULT '0.00' COMMENT '年费',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认角色',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '添加时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用_角色表';

-- --------------------------------------------------------

--
-- 表的结构 `region`
--

CREATE TABLE `region` (
  `id` int(11) NOT NULL COMMENT 'Id',
  `dn` int(11) NOT NULL COMMENT '地区编码',
  `name` varchar(255) NOT NULL COMMENT '地区名称',
  `pid` int(11) DEFAULT '0' COMMENT '上级地区',
  `is_show` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否显示',
  `merchant_id` int(11) DEFAULT NULL COMMENT '租户',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间',
  `level` int(11) NOT NULL DEFAULT '0' COMMENT '层级',
  `tree` varchar(255) DEFAULT NULL COMMENT '结构树',
  `sort` int(11) DEFAULT '0' COMMENT '排序'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区信息表';

-- --------------------------------------------------------

--
-- 表的结构 `xmjs`
--

CREATE TABLE `xmjs` (
  `id` int(11) NOT NULL,
  `merchant_id` int(11) DEFAULT '0' COMMENT '租户',
  `xh` varchar(128) NOT NULL COMMENT '序号',
  `xmmc` varchar(255) NOT NULL COMMENT '项目名称',
  `bd_name` varchar(255) DEFAULT NULL COMMENT '标段或实施事项名称',
  `sheng` varchar(255) DEFAULT NULL COMMENT '省',
  `si` varchar(255) DEFAULT NULL COMMENT '市',
  `xian` varchar(255) DEFAULT NULL COMMENT '县',
  `xianz` text COMMENT '标段所在乡镇',
  `chun` text COMMENT '标段所在村',
  `jl_name` text COMMENT '该标段监理单位名称',
  `jl_zz` text COMMENT '监理单位资质',
  `jl_kt` varchar(255) DEFAULT NULL COMMENT '监理合同签订日期',
  `jl_money` varchar(255) DEFAULT NULL COMMENT '监理合同金额',
  `zbms` varchar(255) DEFAULT NULL COMMENT '该标段招标模式',
  `zbsj` text COMMENT '该标段发布招标公告时间',
  `zbdlgs` text COMMENT '招标代理机构公司名称',
  `zbdwmc` text COMMENT '中标单位名称',
  `zbdwxz` text COMMENT '中标单位性质',
  `zbdwzz` text COMMENT '中标单位资质',
  `plan_ztz` varchar(128) DEFAULT NULL COMMENT '计划总投资额',
  `plan_ztz_kt_money` varchar(128) DEFAULT NULL COMMENT '计划总投资额中施工合同金额',
  `content` text COMMENT '主要建设内容',
  `beign_date` varchar(255) DEFAULT NULL COMMENT '实际开工或开始实施时间',
  `jssj` varchar(255) DEFAULT NULL COMMENT '结算时间',
  `js_money` varchar(255) DEFAULT NULL COMMENT '结算金额',
  `js_date` varchar(255) DEFAULT NULL COMMENT '决算时间',
  `js_money2` varchar(255) DEFAULT NULL COMMENT '决算金额',
  `zbdw_main` text COMMENT '中标单位联系人姓名',
  `zbdw_mobile` text COMMENT '中标单位联系人手机号',
  `remark` text COMMENT '备注',
  `status` tinyint(4) DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `xmjx`
--

CREATE TABLE `xmjx` (
  `id` int(11) NOT NULL COMMENT 'ID',
  `merchant_id` int(11) DEFAULT '0' COMMENT '租户',
  `xh` int(11) DEFAULT NULL COMMENT '序号',
  `xmmc` varchar(255) NOT NULL COMMENT '项目名称',
  `sheng` varchar(255) DEFAULT NULL COMMENT '省',
  `si` varchar(128) DEFAULT NULL COMMENT '市',
  `xian` varchar(128) DEFAULT NULL COMMENT '县',
  `xiangz` varchar(255) DEFAULT NULL COMMENT '乡镇',
  `chun` text COMMENT '村',
  `sheng_zg` varchar(255) DEFAULT NULL COMMENT '省主管单位',
  `si_zg` varchar(255) DEFAULT NULL COMMENT '市主管单位',
  `xian_zg` varchar(255) DEFAULT NULL COMMENT '县主管单位',
  `content` text COMMENT '建设内容',
  `task_nf` varchar(128) DEFAULT NULL COMMENT '任务年份',
  `jxmj` varchar(128) DEFAULT NULL COMMENT '建设面积',
  `last_check_unit` varchar(128) DEFAULT NULL COMMENT '终审单位',
  `check_level` varchar(128) DEFAULT NULL COMMENT '审批级次',
  `pifu_name` text COMMENT '批复文件名称',
  `pifu_date` varchar(128) DEFAULT NULL COMMENT '批复日期',
  `build_date` varchar(128) DEFAULT NULL COMMENT '实施日期',
  `begin_date` varchar(128) DEFAULT NULL COMMENT '实际开工日期',
  `success_date` varchar(128) DEFAULT NULL COMMENT '完工时间',
  `last_date` varchar(128) DEFAULT NULL COMMENT '实际完工时间',
  `is_zb` varchar(128) DEFAULT NULL COMMENT '是否招标',
  `bd_num` varchar(128) DEFAULT NULL COMMENT '划分标段',
  `last_check_date` varchar(128) DEFAULT NULL COMMENT '最终验收时间',
  `last_check_unit_bd` text COMMENT '最终验收部门',
  `jg_date` varchar(128) DEFAULT NULL COMMENT '竣工时间',
  `jg_money` varchar(128) DEFAULT NULL COMMENT '竣工决算金额',
  `zhj` text COMMENT '总合计',
  `tdpzmj` text COMMENT '土地平整面积',
  `tdpz_money` varchar(128) DEFAULT NULL COMMENT '土地平整投入资金',
  `trglmj` text COMMENT '土壤改良面积',
  `gghpsss` text COMMENT '灌溉和排水设施',
  `tjdl` text COMMENT '田间道路',
  `ntffrst` text COMMENT '农田防护与生态环境保护',
  `ntspd` text COMMENT '农田输配电',
  `zcyj` text COMMENT '项目建成后接受资产移交的所有权主体',
  `js_sign_yj` varchar(128) DEFAULT NULL COMMENT '是否签订资产移交协议',
  `jcssghzl` text COMMENT '项目建成设施管护主体',
  `is_gh` varchar(128) DEFAULT NULL COMMENT '是否签订管护协议',
  `status` tinyint(4) DEFAULT '1' COMMENT '数据状态',
  `created_at` int(11) NOT NULL COMMENT '添加时间',
  `updated_at` int(11) NOT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目建设表';

--
-- 转储表的索引
--

--
-- 表的索引 `api_access_token`
--
ALTER TABLE `api_access_token`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `access_token` (`access_token`),
  ADD UNIQUE KEY `refresh_token` (`refresh_token`),
  ADD KEY `member_id` (`member_id`,`member_type`,`group`),
  ADD KEY `merchant_id` (`merchant_id`);

--
-- 表的索引 `common_action_log`
--
ALTER TABLE `common_action_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `addon_name` (`addon_name`),
  ADD KEY `is_addon` (`is_addon`,`status`);

--
-- 表的索引 `common_addons`
--
ALTER TABLE `common_addons`
  ADD PRIMARY KEY (`id`),
  ADD KEY `name` (`name`),
  ADD KEY `update` (`updated_at`);

--
-- 表的索引 `common_addons_config`
--
ALTER TABLE `common_addons_config`
  ADD PRIMARY KEY (`id`),
  ADD KEY `addon_name` (`addon_name`);

--
-- 表的索引 `common_archives`
--
ALTER TABLE `common_archives`
  ADD PRIMARY KEY (`id`),
  ADD KEY `merchant_id` (`merchant_id`);

--
-- 表的索引 `common_archives_apply`
--
ALTER TABLE `common_archives_apply`
  ADD PRIMARY KEY (`id`),
  ADD KEY `merchant_id` (`merchant_id`);

--
-- 表的索引 `common_attachment`
--
ALTER TABLE `common_attachment`
  ADD PRIMARY KEY (`id`),
  ADD KEY `md5` (`md5`);

--
-- 表的索引 `common_attachment_cate`
--
ALTER TABLE `common_attachment_cate`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `common_bank_number`
--
ALTER TABLE `common_bank_number`
  ADD PRIMARY KEY (`id`),
  ADD KEY `bank_name` (`bank_name`);

--
-- 表的索引 `common_config`
--
ALTER TABLE `common_config`
  ADD PRIMARY KEY (`id`),
  ADD KEY `type` (`type`),
  ADD KEY `group` (`cate_id`),
  ADD KEY `uk_name` (`name`);

--
-- 表的索引 `common_config_cate`
--
ALTER TABLE `common_config_cate`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `common_config_value`
--
ALTER TABLE `common_config_value`
  ADD PRIMARY KEY (`id`),
  ADD KEY `config_id` (`config_id`);

--
-- 表的索引 `common_log`
--
ALTER TABLE `common_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `error_code` (`error_code`),
  ADD KEY `req_id` (`req_id`),
  ADD KEY `ip` (`ip`),
  ADD KEY `created_at` (`created_at`),
  ADD KEY `status` (`status`,`created_at`);

--
-- 表的索引 `common_menu`
--
ALTER TABLE `common_menu`
  ADD PRIMARY KEY (`id`),
  ADD KEY `url` (`url`);

--
-- 表的索引 `common_menu_cate`
--
ALTER TABLE `common_menu_cate`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `common_notify`
--
ALTER TABLE `common_notify`
  ADD PRIMARY KEY (`id`),
  ADD KEY `target_type` (`target_type`),
  ADD KEY `merchant_id` (`merchant_id`);

--
-- 表的索引 `common_notify_announce`
--
ALTER TABLE `common_notify_announce`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `common_notify_config`
--
ALTER TABLE `common_notify_config`
  ADD PRIMARY KEY (`id`),
  ADD KEY `name` (`name`),
  ADD KEY `addon_name` (`addon_name`);

--
-- 表的索引 `common_notify_member`
--
ALTER TABLE `common_notify_member`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `common_notify_pull_time`
--
ALTER TABLE `common_notify_pull_time`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `common_provinces`
--
ALTER TABLE `common_provinces`
  ADD PRIMARY KEY (`id`),
  ADD KEY `parentid` (`pid`),
  ADD KEY `tree` (`tree`);

--
-- 表的索引 `common_report_log`
--
ALTER TABLE `common_report_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `log_id` (`log_id`);

--
-- 表的索引 `common_theme`
--
ALTER TABLE `common_theme`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`);

--
-- 表的索引 `dict`
--
ALTER TABLE `dict`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `dict_item`
--
ALTER TABLE `dict_item`
  ADD PRIMARY KEY (`id`),
  ADD KEY `dict_id` (`dict_id`);

--
-- 表的索引 `download_task`
--
ALTER TABLE `download_task`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `extend_config`
--
ALTER TABLE `extend_config`
  ADD PRIMARY KEY (`id`),
  ADD KEY `type` (`type`),
  ADD KEY `uk_name` (`name`);

--
-- 表的索引 `extend_pay_log`
--
ALTER TABLE `extend_pay_log`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `extend_pay_refund`
--
ALTER TABLE `extend_pay_refund`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_sn` (`order_sn`);

--
-- 表的索引 `extend_sms_log`
--
ALTER TABLE `extend_sms_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `error_code` (`error_code`);

--
-- 表的索引 `hccj`
--
ALTER TABLE `hccj`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `member`
--
ALTER TABLE `member`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `member_account`
--
ALTER TABLE `member_account`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `merchant_id` (`merchant_id`,`member_type`);

--
-- 表的索引 `member_address`
--
ALTER TABLE `member_address`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`);

--
-- 表的索引 `member_auth`
--
ALTER TABLE `member_auth`
  ADD PRIMARY KEY (`id`),
  ADD KEY `oauth_client` (`oauth_client`,`oauth_client_user_id`),
  ADD KEY `member_id` (`member_id`);

--
-- 表的索引 `member_bank_account`
--
ALTER TABLE `member_bank_account`
  ADD PRIMARY KEY (`id`),
  ADD KEY `IDX_member_bank_account_uid` (`member_id`);

--
-- 表的索引 `member_cancel`
--
ALTER TABLE `member_cancel`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `member_certification`
--
ALTER TABLE `member_certification`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`);

--
-- 表的索引 `member_credits_log`
--
ALTER TABLE `member_credits_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `member_type` (`member_type`,`status`);

--
-- 表的索引 `member_invoice`
--
ALTER TABLE `member_invoice`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `member_level`
--
ALTER TABLE `member_level`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `member_level_config`
--
ALTER TABLE `member_level_config`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `member_stat`
--
ALTER TABLE `member_stat`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`);

--
-- 表的索引 `member_tag`
--
ALTER TABLE `member_tag`
  ADD PRIMARY KEY (`id`),
  ADD KEY `tag_id` (`id`);

--
-- 表的索引 `member_tag_map`
--
ALTER TABLE `member_tag_map`
  ADD KEY `tag_id` (`tag_id`),
  ADD KEY `article_id` (`member_id`);

--
-- 表的索引 `member_withdraw_deposit`
--
ALTER TABLE `member_withdraw_deposit`
  ADD PRIMARY KEY (`id`),
  ADD KEY `withdraw_no` (`withdraw_no`),
  ADD KEY `merchant_id` (`merchant_id`,`member_id`);

--
-- 表的索引 `merchant`
--
ALTER TABLE `merchant`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `migration`
--
ALTER TABLE `migration`
  ADD PRIMARY KEY (`version`);

--
-- 表的索引 `mp_list`
--
ALTER TABLE `mp_list`
  ADD PRIMARY KEY (`id`),
  ADD KEY `region_id` (`region_id`),
  ADD KEY `project_id` (`project_id`);

--
-- 表的索引 `mp_live`
--
ALTER TABLE `mp_live`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `mp_question`
--
ALTER TABLE `mp_question`
  ADD PRIMARY KEY (`id`),
  ADD KEY `project_id` (`project_id`);

--
-- 表的索引 `oauth2_access_token`
--
ALTER TABLE `oauth2_access_token`
  ADD PRIMARY KEY (`id`),
  ADD KEY `client_id` (`client_id`),
  ADD KEY `access_token` (`access_token`);

--
-- 表的索引 `oauth2_authorization_code`
--
ALTER TABLE `oauth2_authorization_code`
  ADD PRIMARY KEY (`authorization_code`),
  ADD KEY `authorization_code` (`authorization_code`);

--
-- 表的索引 `oauth2_client`
--
ALTER TABLE `oauth2_client`
  ADD PRIMARY KEY (`id`),
  ADD KEY `client_id` (`client_id`);

--
-- 表的索引 `oauth2_refresh_token`
--
ALTER TABLE `oauth2_refresh_token`
  ADD PRIMARY KEY (`refresh_token`),
  ADD KEY `client_id` (`client_id`);

--
-- 表的索引 `project`
--
ALTER TABLE `project`
  ADD PRIMARY KEY (`id`),
  ADD KEY `region` (`region_id`);

--
-- 表的索引 `project_build`
--
ALTER TABLE `project_build`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `project_file`
--
ALTER TABLE `project_file`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `project_pifu`
--
ALTER TABLE `project_pifu`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `rbac_auth_item`
--
ALTER TABLE `rbac_auth_item`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `rbac_auth_item_child`
--
ALTER TABLE `rbac_auth_item_child`
  ADD KEY `role_id` (`role_id`),
  ADD KEY `item_id` (`item_id`);

--
-- 表的索引 `rbac_auth_role`
--
ALTER TABLE `rbac_auth_role`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `region`
--
ALTER TABLE `region`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `xmjs`
--
ALTER TABLE `xmjs`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `xmjx`
--
ALTER TABLE `xmjx`
  ADD PRIMARY KEY (`id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `api_access_token`
--
ALTER TABLE `api_access_token`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_action_log`
--
ALTER TABLE `common_action_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `common_addons`
--
ALTER TABLE `common_addons`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `common_addons_config`
--
ALTER TABLE `common_addons_config`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `common_archives`
--
ALTER TABLE `common_archives`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_archives_apply`
--
ALTER TABLE `common_archives_apply`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_attachment`
--
ALTER TABLE `common_attachment`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_attachment_cate`
--
ALTER TABLE `common_attachment_cate`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `common_bank_number`
--
ALTER TABLE `common_bank_number`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_config`
--
ALTER TABLE `common_config`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `common_config_cate`
--
ALTER TABLE `common_config_cate`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `common_config_value`
--
ALTER TABLE `common_config_value`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `common_log`
--
ALTER TABLE `common_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_menu`
--
ALTER TABLE `common_menu`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_menu_cate`
--
ALTER TABLE `common_menu_cate`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `common_notify`
--
ALTER TABLE `common_notify`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `common_notify_announce`
--
ALTER TABLE `common_notify_announce`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `common_notify_config`
--
ALTER TABLE `common_notify_config`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_notify_member`
--
ALTER TABLE `common_notify_member`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_notify_pull_time`
--
ALTER TABLE `common_notify_pull_time`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_report_log`
--
ALTER TABLE `common_report_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `common_theme`
--
ALTER TABLE `common_theme`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `dict`
--
ALTER TABLE `dict`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Id';

--
-- 使用表AUTO_INCREMENT `dict_item`
--
ALTER TABLE `dict_item`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id';

--
-- 使用表AUTO_INCREMENT `download_task`
--
ALTER TABLE `download_task`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `extend_config`
--
ALTER TABLE `extend_config`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `extend_pay_log`
--
ALTER TABLE `extend_pay_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `extend_pay_refund`
--
ALTER TABLE `extend_pay_refund`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id';

--
-- 使用表AUTO_INCREMENT `extend_sms_log`
--
ALTER TABLE `extend_sms_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `hccj`
--
ALTER TABLE `hccj`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member`
--
ALTER TABLE `member`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member_account`
--
ALTER TABLE `member_account`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member_address`
--
ALTER TABLE `member_address`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `member_auth`
--
ALTER TABLE `member_auth`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `member_bank_account`
--
ALTER TABLE `member_bank_account`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member_cancel`
--
ALTER TABLE `member_cancel`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member_certification`
--
ALTER TABLE `member_certification`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member_credits_log`
--
ALTER TABLE `member_credits_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member_invoice`
--
ALTER TABLE `member_invoice`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member_level`
--
ALTER TABLE `member_level`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `member_level_config`
--
ALTER TABLE `member_level_config`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member_stat`
--
ALTER TABLE `member_stat`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member_tag`
--
ALTER TABLE `member_tag`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `member_withdraw_deposit`
--
ALTER TABLE `member_withdraw_deposit`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `merchant`
--
ALTER TABLE `merchant`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `mp_list`
--
ALTER TABLE `mp_list`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id';

--
-- 使用表AUTO_INCREMENT `mp_live`
--
ALTER TABLE `mp_live`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Id';

--
-- 使用表AUTO_INCREMENT `mp_question`
--
ALTER TABLE `mp_question`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `oauth2_access_token`
--
ALTER TABLE `oauth2_access_token`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `oauth2_client`
--
ALTER TABLE `oauth2_client`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `project`
--
ALTER TABLE `project`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id';

--
-- 使用表AUTO_INCREMENT `project_build`
--
ALTER TABLE `project_build`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id';

--
-- 使用表AUTO_INCREMENT `project_file`
--
ALTER TABLE `project_file`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id';

--
-- 使用表AUTO_INCREMENT `project_pifu`
--
ALTER TABLE `project_pifu`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id';

--
-- 使用表AUTO_INCREMENT `rbac_auth_item`
--
ALTER TABLE `rbac_auth_item`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `rbac_auth_role`
--
ALTER TABLE `rbac_auth_role`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

--
-- 使用表AUTO_INCREMENT `region`
--
ALTER TABLE `region`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Id';

--
-- 使用表AUTO_INCREMENT `xmjs`
--
ALTER TABLE `xmjs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `xmjx`
--
ALTER TABLE `xmjx`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 限制导出的表
--

--
-- 限制表 `dict_item`
--
ALTER TABLE `dict_item`
  ADD CONSTRAINT `dict_id` FOREIGN KEY (`dict_id`) REFERENCES `dict` (`id`);

--
-- 限制表 `mp_list`
--
ALTER TABLE `mp_list`
  ADD CONSTRAINT `project_id` FOREIGN KEY (`project_id`) REFERENCES `project` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `region_id` FOREIGN KEY (`region_id`) REFERENCES `region` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- 限制表 `project`
--
ALTER TABLE `project`
  ADD CONSTRAINT `region` FOREIGN KEY (`region_id`) REFERENCES `region` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
