package tag

import (
	"context"
	"fmt"
	"mp/rpc/member"

	"mp/api/internal/svc"
	"mp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除标签
func NewDeleteTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteTagLogic {
	return &DeleteTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteTagLogic) DeleteTag(req *types.DeleteTagReq) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	_, err = l.svcCtx.MemberRpc.DeleteTag(l.ctx, &member.DeleteTagReq{
		Id: req.Id,
	})
	if err != nil {
		return nil, fmt.Erro<PERSON>("调用 RPC DeleteTag 服务 失败 %v", err)
	}
	return &types.CommonResponse{
		Code:    200,
		Message: "调用 RPC DeleteTag 服务 成功",
	}, nil
}
