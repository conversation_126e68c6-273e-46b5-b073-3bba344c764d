package tag

import (
	"context"
	"fmt"
	"mp/rpc/member"

	"mp/api/internal/svc"
	"mp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新标签
func NewUpdateTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateTagLogic {
	return &UpdateTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateTagLogic) UpdateTag(req *types.UpdateTagReq) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	rpcResp, err := l.svcCtx.MemberRpc.UpdateTag(l.ctx, &member.UpdateTagReq{
		Title:  req.Title,
		Sort:   int32(req.Sort),
		Status: int32(req.Status),
	})
	if err != nil {
		return nil, fmt.Errorf("调用 RPC UpdateTag 服务 失败 %v", err)
	}
	return &types.CommonResponse{
		Code:    200,
		Message: "调用 RPC UpdateTag 服务 成功",
		Data:    rpcResp,
	}, nil
}
