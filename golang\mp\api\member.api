syntax = "v1"

info (
	title:   "会员标签管理API"
	desc:    "提供会员和标签的增删改查功能"
	author:  "System"
	version: "1.0"
)

// 通用响应
type CommonResponse {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 分页请求
type PageRequest {
	Page     int `form:"page,default=1"`
	PageSize int `form:"pageSize,default=20"`
}

// 会员信息
type Member {
	Id                  int64  `json:"id"`
	MerchantId          int64  `json:"merchantId"`
	StoreId             int64  `json:"storeId"`
	Username            string `json:"username"`
	PasswordHash        string `json:"passwordHash,omitempty"`
	AuthKey             string `json:"authKey,omitempty"`
	PasswordResetToken  string `json:"passwordResetToken,omitempty"`
	MobileResetToken    string `json:"mobileResetToken,omitempty"`
	Type                int    `json:"type,omitempty"`
	Realname            string `json:"realname"`
	Nickname            string `json:"nickname"`
	HeadPortrait        string `json:"headPortrait"`
	Gender              int    `json:"gender"`
	Qq                  string `json:"qq"`
	Email               string `json:"email"`
	Birthday            string `json:"birthday"`
	ProvinceId          int64  `json:"provinceId"`
	CityId              int64  `json:"cityId"`
	AreaId              int64  `json:"areaId"`
	Address             string `json:"address"`
	Mobile              string `json:"mobile"`
	TelNo               string `json:"telNo"`
	BgImage             string `json:"bgImage"`
	Description         string `json:"description"`
	VisitCount          int    `json:"visitCount"`
	LastTime            int64  `json:"lastTime"`
	LastIp              string `json:"lastIp"`
	Role                int    `json:"role"`
	CurrentLevel        int    `json:"currentLevel"`
	LevelExpirationTime int64  `json:"levelExpirationTime"`
	LevelBuyType        int    `json:"levelBuyType"`
	Pid                 int64  `json:"pid"`
	Level               int    `json:"level"`
	Tree                string `json:"tree"`
	PromoterCode        string `json:"promoterCode"`
	CertificationType   int    `json:"certificationType"`
	Source              string `json:"source"`
	Status              int    `json:"status"`
	CreatedAt           int64  `json:"createdAt"`
	UpdatedAt           int64  `json:"updatedAt"`
	RegionId            int64  `json:"regionId"`
}

// 标签信息
type Tag {
	Id         int64  `json:"id"`
	MerchantId int64  `json:"merchantId"`
	StoreId    int64  `json:"storeId"`
	Title      string `json:"title"`
	Sort       int    `json:"sort"`
	Status     int    `json:"status"`
	CreatedAt  int64  `json:"createdAt"`
	UpdatedAt  int64  `json:"updatedAt"`
}

// 会员请求
type (
	GetMemberReq {
		Id int64 `path:"id"`
	}
	ListMembersReq {
		PageRequest
		MerchantId int64  `form:"merchantId,optional"`
		StoreId    int64  `form:"storeId,optional"`
		Keyword    string `form:"keyword,optional"`
		Status     int    `form:"status,optional"`
	}
	ListMembersResp {
		Total int64    `json:"total"`
		List  []Member `json:"list"`
	}
	CreateMemberReq {
		MerchantId          int64  `json:"merchantId,optional"`
		StoreId             int64  `json:"storeId,optional"`
		Username            string `json:"username"`
		Password            string `json:"password"`
		Type                int    `json:"type,optional"`
		Realname            string `json:"realname,optional"`
		Nickname            string `json:"nickname,optional"`
		HeadPortrait        string `json:"headPortrait,optional"`
		Gender              int    `json:"gender,optional"`
		Qq                  string `json:"qq,optional"`
		Email               string `json:"email,optional"`
		Birthday            string `json:"birthday,optional"`
		ProvinceId          int64  `json:"provinceId,optional"`
		CityId              int64  `json:"cityId,optional"`
		AreaId              int64  `json:"areaId,optional"`
		Address             string `json:"address,optional"`
		Mobile              string `json:"mobile,optional"`
		TelNo               string `json:"telNo,optional"`
		BgImage             string `json:"bgImage,optional"`
		Description         string `json:"description,optional"`
		Role                int    `json:"role,optional"`
		CurrentLevel        int    `json:"currentLevel,optional"`
		LevelExpirationTime int64  `json:"levelExpirationTime,optional"`
		LevelBuyType        int    `json:"levelBuyType,optional"`
		Pid                 int64  `json:"pid,optional"`
		Level               int    `json:"level,optional"`
		Tree                string `json:"tree,optional"`
		PromoterCode        string `json:"promoterCode,optional"`
		CertificationType   int    `json:"certificationType,optional"`
		Source              string `json:"source,optional"`
		Status              int    `json:"status,optional"`
		RegionId            int64  `json:"regionId,optional"`
	}
	UpdateMemberReq {
		Id                  int64  `path:"id"`
		MerchantId          int64  `json:"merchantId,optional"`
		StoreId             int64  `json:"storeId,optional"`
		Username            string `json:"username,optional"`
		Type                int    `json:"type,optional"`
		Realname            string `json:"realname,optional"`
		Nickname            string `json:"nickname,optional"`
		HeadPortrait        string `json:"headPortrait,optional"`
		Gender              int    `json:"gender,optional"`
		Qq                  string `json:"qq,optional"`
		Email               string `json:"email,optional"`
		Birthday            string `json:"birthday,optional"`
		ProvinceId          int64  `json:"provinceId,optional"`
		CityId              int64  `json:"cityId,optional"`
		AreaId              int64  `json:"areaId,optional"`
		Address             string `json:"address,optional"`
		Mobile              string `json:"mobile,optional"`
		TelNo               string `json:"telNo,optional"`
		BgImage             string `json:"bgImage,optional"`
		Description         string `json:"description,optional"`
		VisitCount          int    `json:"visitCount,optional"`
		LastTime            int64  `json:"lastTime,optional"`
		LastIp              string `json:"lastIp,optional"`
		Role                int    `json:"role,optional"`
		CurrentLevel        int    `json:"currentLevel,optional"`
		LevelExpirationTime int64  `json:"levelExpirationTime,optional"`
		LevelBuyType        int    `json:"levelBuyType,optional"`
		Pid                 int64  `json:"pid,optional"`
		Level               int    `json:"level,optional"`
		Tree                string `json:"tree,optional"`
		PromoterCode        string `json:"promoterCode,optional"`
		CertificationType   int    `json:"certificationType,optional"`
		Source              string `json:"source,optional"`
		Status              int    `json:"status,optional"`
		RegionId            int64  `json:"regionId,optional"`
	}
	DeleteMemberReq {
		Id int64 `path:"id"`
	}
)

// 标签请求
type (
	GetTagReq {
		Id int64 `path:"id"`
	}
	ListTagsReq {
		PageRequest
		MerchantId int64  `form:"merchantId,optional"`
		StoreId    int64  `form:"storeId,optional"`
		Keyword    string `form:"keyword,optional"`
		Status     int    `form:"status,optional"`
	}
	ListTagsResp {
		Total int64 `json:"total"`
		List  []Tag `json:"list"`
	}
	CreateTagReq {
		MerchantId int64  `json:"merchantId"`
		StoreId    int64  `json:"storeId,optional"`
		Title      string `json:"title"`
		Sort       int    `json:"sort,optional"`
		Status     int    `json:"status,optional"`
	}
	UpdateTagReq {
		Id     int64  `path:"id"`
		Title  string `json:"title,optional"`
		Sort   int    `json:"sort,optional"`
		Status int    `json:"status,optional"`
	}
	DeleteTagReq {
		Id int64 `path:"id"`
	}
)

// 会员标签关联
type (
	AddTagsToMemberReq {
		MemberId int64   `json:"memberId"`
		TagIds   []int64 `json:"tagIds"`
	}
	RemoveTagsFromMemberReq {
		MemberId int64   `json:"memberId"`
		TagIds   []int64 `json:"tagIds"`
	}
	GetMemberTagsReq {
		MemberId int64 `path:"memberId"`
	}
)

// 会员API定义
@server (
	group:  member
	prefix: /api/v1/members
)
service member-api {
	@doc "获取会员详情"
	@handler getMember
	get /:id (GetMemberReq) returns (CommonResponse)

	@doc "会员列表"
	@handler listMembers
	get / (ListMembersReq) returns (CommonResponse)

	@doc "创建会员"
	@handler createMember
	post / (CreateMemberReq) returns (CommonResponse)

	@doc "更新会员"
	@handler updateMember
	put /:id (UpdateMemberReq) returns (CommonResponse)

	@doc "删除会员"
	@handler deleteMember
	delete /:id (DeleteMemberReq) returns (CommonResponse)
}

// 标签API定义
@server (
	group:  tag
	prefix: /api/v1/tags
)
service member-api {
	@doc "获取标签详情"
	@handler getTag
	get /:id (GetTagReq) returns (CommonResponse)

	@doc "标签列表"
	@handler listTags
	get / (ListTagsReq) returns (CommonResponse)

	@doc "创建标签"
	@handler createTag
	post / (CreateTagReq) returns (CommonResponse)

	@doc "更新标签"
	@handler updateTag
	put /:id (UpdateTagReq) returns (CommonResponse)

	@doc "删除标签"
	@handler deleteTag
	delete /:id (DeleteTagReq) returns (CommonResponse)

	@doc "获取会员标签"
	@handler getMemberTags
	get /member/:memberId (GetMemberTagsReq) returns (CommonResponse)
}

// 会员标签关联API定义
@server (
	group:  memberTag
	prefix: /api/v1/memberTags
)
service member-api {
	@doc "为会员添加标签"
	@handler addTagsToMember
	post / (AddTagsToMemberReq) returns (CommonResponse)

	@doc "移除会员标签"
	@handler removeTagsFromMember
	delete / (RemoveTagsFromMemberReq) returns (CommonResponse)
}

