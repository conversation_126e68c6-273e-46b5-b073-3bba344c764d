package memberTag

import (
	"context"
	"fmt"
	"mp/rpc/model"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type RemoveTagsFromMemberLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRemoveTagsFromMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveTagsFromMemberLogic {
	return &RemoveTagsFromMemberLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *RemoveTagsFromMemberLogic) RemoveTagsFromMember(in *member.RemoveTagsFromMemberReq) (*member.CommonResp, error) {
	// todo: add your logic here and delete this line
	// 添加日志
	l.Infof("接收到删除会员标签请求: %+v", in)

	// 检查DB是否为nil
	if l.svcCtx.DB == nil {
		return nil, fmt.Errorf("数据库连接为空，请检查数据库配置")
	}

	// 开启事务
	tx := l.svcCtx.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 应该添加删除标签关联的代码，类似：
	for _, tagID := range in.TagIds {
		if err := tx.Where("member_id = ? AND tag_id = ?", in.MemberId, tagID).Delete(&model.MemberTagMap{}).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("删除标签关联失败: %v", err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	return &member.CommonResp{
		Success: true,
		Message: "使用事务删除成功",
	}, nil
}
