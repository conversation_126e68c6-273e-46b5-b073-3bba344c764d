package member

import (
	"context"
	"fmt"
	"mp/rpc/member"

	"mp/api/internal/svc"
	"mp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMemberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取会员详情
func NewGetMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberLogic {
	return &GetMemberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMemberLogic) GetMember(req *types.GetMemberReq) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	rpcResp, err := l.svcCtx.MemberRpc.GetMember(l.ctx, &member.GetMemberReq{
		Id: req.Id,
	})

	if err != nil {
		return nil, fmt.Erro<PERSON>("调用 RPC GetMember 服务 失败 %v", err)
	}

	return &types.CommonResponse{
		Code:    200,
		Message: "调用 RPC GetMember 服务 成功",
		Data:    rpcResp,
	}, nil
}
