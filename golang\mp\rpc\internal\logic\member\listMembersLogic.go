package member

import (
	"context"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListMembersLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListMembersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListMembersLogic {
	return &ListMembersLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *ListMembersLogic) ListMembers(in *member.ListMembersReq) (*member.ListMembersResp, error) {
	// todo: add your logic here and delete this line

	return &member.ListMembersResp{}, nil
}
