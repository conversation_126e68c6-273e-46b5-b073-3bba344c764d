package JWT

import (
	"github.com/golang-jwt/jwt/v4"
)

// GetJwtToken 生成JWT令牌
func GetJwtToken(secretKey string, iat, seconds int64, userId int) (string, error) {
	claims := make(jwt.MapClaims)
	claims["exp"] = iat + seconds
	claims["iat"] = iat
	claims["userId"] = userId
	token := jwt.New(jwt.SigningMethodHS256)
	token.Claims = claims
	return token.SignedString([]byte(secretKey))
}

// ParseToken 解析JWT令牌
func ParseToken(tokenString string, secretKey string) (int, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})

	if err != nil {
		return 0, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		userId := int(claims["userId"].(float64))
		return userId, nil
	}

	return 0, err
}
