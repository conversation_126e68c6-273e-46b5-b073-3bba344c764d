version: '3'

tasks:
  ps:
    dir: ./
    cmds:
      - docker ps -a

  run:
    dir: ./
    cmds:
      - docker-compose up -d

  build:
    dir: ./
    cmds:
      - docker-compose up -d --build mp-api mp-rpc

  cache:
    dir: ./
    cmds:
      - docker-compose build --no-cache mp-api mp-rpc

  docker_api:
    dir: ./api
    cmds:
      - goctl docker -go dict.go

  docker_rpc:
    dir: ./rpc
    cmds:
      - goctl docker -go dict.go

  api:
    dir: ./api/desc
    cmds:
      - goctl api go -api user.api -dir ../ -style gozero

  rpc:
    dir: ./rpc/pb
    cmds:
      - goctl rpc protoc user.proto --go_out=. --go-grpc_out=. --zrpc_out=.

  apilog:
    dir: ./api
    cmds:
      - docker logs --tail 50 golang-docker-compose-main-mp-api-1

  rpclog:
    dir: ./rpc
    cmds:
      - docker logs --tail 50 golang-docker-compose-main-mp-rpc-1