// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: member.proto

package memberclient

import (
	"context"

	"mp/rpc/member"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AddTagsToMemberReq      = member.AddTagsToMemberReq
	CommonResp              = member.CommonResp
	CreateMemberReq         = member.CreateMemberReq
	CreateTagReq            = member.CreateTagReq
	DeleteMemberReq         = member.DeleteMemberReq
	DeleteTagReq            = member.DeleteTagReq
	GetMemberReq            = member.GetMemberReq
	GetMemberTagsReq        = member.GetMemberTagsReq
	GetTagReq               = member.GetTagReq
	ListMembersReq          = member.ListMembersReq
	ListMembersResp         = member.ListMembersResp
	ListTagsReq             = member.ListTagsReq
	ListTagsResp            = member.ListTagsResp
	MemberInfo              = member.MemberInfo
	RemoveTagsFromMemberReq = member.RemoveTagsFromMemberReq
	TagInfo                 = member.TagInfo
	UpdateMemberReq         = member.UpdateMemberReq
	UpdateTagReq            = member.UpdateTagReq

	Member interface {
		// 会员管理
		GetMember(ctx context.Context, in *GetMemberReq, opts ...grpc.CallOption) (*MemberInfo, error)
		ListMembers(ctx context.Context, in *ListMembersReq, opts ...grpc.CallOption) (*ListMembersResp, error)
		CreateMember(ctx context.Context, in *CreateMemberReq, opts ...grpc.CallOption) (*MemberInfo, error)
		UpdateMember(ctx context.Context, in *UpdateMemberReq, opts ...grpc.CallOption) (*MemberInfo, error)
		DeleteMember(ctx context.Context, in *DeleteMemberReq, opts ...grpc.CallOption) (*CommonResp, error)
		// 标签管理
		GetTag(ctx context.Context, in *GetTagReq, opts ...grpc.CallOption) (*TagInfo, error)
		ListTags(ctx context.Context, in *ListTagsReq, opts ...grpc.CallOption) (*ListTagsResp, error)
		CreateTag(ctx context.Context, in *CreateTagReq, opts ...grpc.CallOption) (*TagInfo, error)
		UpdateTag(ctx context.Context, in *UpdateTagReq, opts ...grpc.CallOption) (*TagInfo, error)
		DeleteTag(ctx context.Context, in *DeleteTagReq, opts ...grpc.CallOption) (*CommonResp, error)
		// 会员标签关联
		AddTagsToMember(ctx context.Context, in *AddTagsToMemberReq, opts ...grpc.CallOption) (*CommonResp, error)
		RemoveTagsFromMember(ctx context.Context, in *RemoveTagsFromMemberReq, opts ...grpc.CallOption) (*CommonResp, error)
		GetMemberTags(ctx context.Context, in *GetMemberTagsReq, opts ...grpc.CallOption) (*ListTagsResp, error)
	}

	defaultMember struct {
		cli zrpc.Client
	}
)

func NewMember(cli zrpc.Client) Member {
	return &defaultMember{
		cli: cli,
	}
}

// 会员管理
func (m *defaultMember) GetMember(ctx context.Context, in *GetMemberReq, opts ...grpc.CallOption) (*MemberInfo, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.GetMember(ctx, in, opts...)
}

func (m *defaultMember) ListMembers(ctx context.Context, in *ListMembersReq, opts ...grpc.CallOption) (*ListMembersResp, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.ListMembers(ctx, in, opts...)
}

func (m *defaultMember) CreateMember(ctx context.Context, in *CreateMemberReq, opts ...grpc.CallOption) (*MemberInfo, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.CreateMember(ctx, in, opts...)
}

func (m *defaultMember) UpdateMember(ctx context.Context, in *UpdateMemberReq, opts ...grpc.CallOption) (*MemberInfo, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.UpdateMember(ctx, in, opts...)
}

func (m *defaultMember) DeleteMember(ctx context.Context, in *DeleteMemberReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.DeleteMember(ctx, in, opts...)
}

// 标签管理
func (m *defaultMember) GetTag(ctx context.Context, in *GetTagReq, opts ...grpc.CallOption) (*TagInfo, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.GetTag(ctx, in, opts...)
}

func (m *defaultMember) ListTags(ctx context.Context, in *ListTagsReq, opts ...grpc.CallOption) (*ListTagsResp, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.ListTags(ctx, in, opts...)
}

func (m *defaultMember) CreateTag(ctx context.Context, in *CreateTagReq, opts ...grpc.CallOption) (*TagInfo, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.CreateTag(ctx, in, opts...)
}

func (m *defaultMember) UpdateTag(ctx context.Context, in *UpdateTagReq, opts ...grpc.CallOption) (*TagInfo, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.UpdateTag(ctx, in, opts...)
}

func (m *defaultMember) DeleteTag(ctx context.Context, in *DeleteTagReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.DeleteTag(ctx, in, opts...)
}

// 会员标签关联
func (m *defaultMember) AddTagsToMember(ctx context.Context, in *AddTagsToMemberReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.AddTagsToMember(ctx, in, opts...)
}

func (m *defaultMember) RemoveTagsFromMember(ctx context.Context, in *RemoveTagsFromMemberReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.RemoveTagsFromMember(ctx, in, opts...)
}

func (m *defaultMember) GetMemberTags(ctx context.Context, in *GetMemberTagsReq, opts ...grpc.CallOption) (*ListTagsResp, error) {
	client := member.NewMemberClient(m.cli.Conn())
	return client.GetMemberTags(ctx, in, opts...)
}
