// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.0
// source: member.proto

package member

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 通用响应
type CommonResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonResp) Reset() {
	*x = CommonResp{}
	mi := &file_member_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResp) ProtoMessage() {}

func (x *CommonResp) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResp.ProtoReflect.Descriptor instead.
func (*CommonResp) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{0}
}

func (x *CommonResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CommonResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 会员相关消息
type MemberInfo struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MerchantId          int64                  `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId             int64                  `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Username            string                 `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	Realname            string                 `protobuf:"bytes,5,opt,name=realname,proto3" json:"realname,omitempty"`
	Nickname            string                 `protobuf:"bytes,6,opt,name=nickname,proto3" json:"nickname,omitempty"`
	HeadPortrait        string                 `protobuf:"bytes,7,opt,name=head_portrait,json=headPortrait,proto3" json:"head_portrait,omitempty"`
	Gender              int32                  `protobuf:"varint,8,opt,name=gender,proto3" json:"gender,omitempty"`
	Qq                  string                 `protobuf:"bytes,9,opt,name=qq,proto3" json:"qq,omitempty"`
	Email               string                 `protobuf:"bytes,10,opt,name=email,proto3" json:"email,omitempty"`
	Birthday            string                 `protobuf:"bytes,11,opt,name=birthday,proto3" json:"birthday,omitempty"`
	ProvinceId          int64                  `protobuf:"varint,12,opt,name=province_id,json=provinceId,proto3" json:"province_id,omitempty"`
	CityId              int64                  `protobuf:"varint,13,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	AreaId              int64                  `protobuf:"varint,14,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	Address             string                 `protobuf:"bytes,15,opt,name=address,proto3" json:"address,omitempty"`
	Mobile              string                 `protobuf:"bytes,16,opt,name=mobile,proto3" json:"mobile,omitempty"`
	TelNo               string                 `protobuf:"bytes,17,opt,name=tel_no,json=telNo,proto3" json:"tel_no,omitempty"`
	BgImage             string                 `protobuf:"bytes,18,opt,name=bg_image,json=bgImage,proto3" json:"bg_image,omitempty"`
	Description         string                 `protobuf:"bytes,19,opt,name=description,proto3" json:"description,omitempty"`
	VisitCount          int32                  `protobuf:"varint,20,opt,name=visit_count,json=visitCount,proto3" json:"visit_count,omitempty"`
	LastTime            int64                  `protobuf:"varint,21,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	LastIp              string                 `protobuf:"bytes,22,opt,name=last_ip,json=lastIp,proto3" json:"last_ip,omitempty"`
	Role                int32                  `protobuf:"varint,23,opt,name=role,proto3" json:"role,omitempty"`
	CurrentLevel        int32                  `protobuf:"varint,24,opt,name=current_level,json=currentLevel,proto3" json:"current_level,omitempty"`
	LevelExpirationTime int64                  `protobuf:"varint,25,opt,name=level_expiration_time,json=levelExpirationTime,proto3" json:"level_expiration_time,omitempty"`
	LevelBuyType        int32                  `protobuf:"varint,26,opt,name=level_buy_type,json=levelBuyType,proto3" json:"level_buy_type,omitempty"`
	Pid                 int64                  `protobuf:"varint,27,opt,name=pid,proto3" json:"pid,omitempty"`
	Level               int32                  `protobuf:"varint,28,opt,name=level,proto3" json:"level,omitempty"`
	Tree                string                 `protobuf:"bytes,29,opt,name=tree,proto3" json:"tree,omitempty"`
	PromoterCode        string                 `protobuf:"bytes,30,opt,name=promoter_code,json=promoterCode,proto3" json:"promoter_code,omitempty"`
	CertificationType   int32                  `protobuf:"varint,31,opt,name=certification_type,json=certificationType,proto3" json:"certification_type,omitempty"`
	Source              string                 `protobuf:"bytes,32,opt,name=source,proto3" json:"source,omitempty"`
	Status              int32                  `protobuf:"varint,33,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt           int64                  `protobuf:"varint,34,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt           int64                  `protobuf:"varint,35,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	RegionId            int64                  `protobuf:"varint,36,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *MemberInfo) Reset() {
	*x = MemberInfo{}
	mi := &file_member_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemberInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberInfo) ProtoMessage() {}

func (x *MemberInfo) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberInfo.ProtoReflect.Descriptor instead.
func (*MemberInfo) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{1}
}

func (x *MemberInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MemberInfo) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *MemberInfo) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *MemberInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *MemberInfo) GetRealname() string {
	if x != nil {
		return x.Realname
	}
	return ""
}

func (x *MemberInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *MemberInfo) GetHeadPortrait() string {
	if x != nil {
		return x.HeadPortrait
	}
	return ""
}

func (x *MemberInfo) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *MemberInfo) GetQq() string {
	if x != nil {
		return x.Qq
	}
	return ""
}

func (x *MemberInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *MemberInfo) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *MemberInfo) GetProvinceId() int64 {
	if x != nil {
		return x.ProvinceId
	}
	return 0
}

func (x *MemberInfo) GetCityId() int64 {
	if x != nil {
		return x.CityId
	}
	return 0
}

func (x *MemberInfo) GetAreaId() int64 {
	if x != nil {
		return x.AreaId
	}
	return 0
}

func (x *MemberInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *MemberInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *MemberInfo) GetTelNo() string {
	if x != nil {
		return x.TelNo
	}
	return ""
}

func (x *MemberInfo) GetBgImage() string {
	if x != nil {
		return x.BgImage
	}
	return ""
}

func (x *MemberInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MemberInfo) GetVisitCount() int32 {
	if x != nil {
		return x.VisitCount
	}
	return 0
}

func (x *MemberInfo) GetLastTime() int64 {
	if x != nil {
		return x.LastTime
	}
	return 0
}

func (x *MemberInfo) GetLastIp() string {
	if x != nil {
		return x.LastIp
	}
	return ""
}

func (x *MemberInfo) GetRole() int32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *MemberInfo) GetCurrentLevel() int32 {
	if x != nil {
		return x.CurrentLevel
	}
	return 0
}

func (x *MemberInfo) GetLevelExpirationTime() int64 {
	if x != nil {
		return x.LevelExpirationTime
	}
	return 0
}

func (x *MemberInfo) GetLevelBuyType() int32 {
	if x != nil {
		return x.LevelBuyType
	}
	return 0
}

func (x *MemberInfo) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *MemberInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *MemberInfo) GetTree() string {
	if x != nil {
		return x.Tree
	}
	return ""
}

func (x *MemberInfo) GetPromoterCode() string {
	if x != nil {
		return x.PromoterCode
	}
	return ""
}

func (x *MemberInfo) GetCertificationType() int32 {
	if x != nil {
		return x.CertificationType
	}
	return 0
}

func (x *MemberInfo) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *MemberInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MemberInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *MemberInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *MemberInfo) GetRegionId() int64 {
	if x != nil {
		return x.RegionId
	}
	return 0
}

type GetMemberReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMemberReq) Reset() {
	*x = GetMemberReq{}
	mi := &file_member_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMemberReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMemberReq) ProtoMessage() {}

func (x *GetMemberReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMemberReq.ProtoReflect.Descriptor instead.
func (*GetMemberReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{2}
}

func (x *GetMemberReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListMembersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    int64                  `protobuf:"varint,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId       int64                  `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Keyword       string                 `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	Page          int32                  `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMembersReq) Reset() {
	*x = ListMembersReq{}
	mi := &file_member_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMembersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMembersReq) ProtoMessage() {}

func (x *ListMembersReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMembersReq.ProtoReflect.Descriptor instead.
func (*ListMembersReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{3}
}

func (x *ListMembersReq) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *ListMembersReq) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *ListMembersReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *ListMembersReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListMembersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListMembersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListMembersResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*MemberInfo          `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMembersResp) Reset() {
	*x = ListMembersResp{}
	mi := &file_member_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMembersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMembersResp) ProtoMessage() {}

func (x *ListMembersResp) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMembersResp.ProtoReflect.Descriptor instead.
func (*ListMembersResp) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{4}
}

func (x *ListMembersResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListMembersResp) GetList() []*MemberInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type CreateMemberReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	MerchantId        int64                  `protobuf:"varint,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId           int64                  `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Username          string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password          string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	Realname          string                 `protobuf:"bytes,5,opt,name=realname,proto3" json:"realname,omitempty"`
	Nickname          string                 `protobuf:"bytes,6,opt,name=nickname,proto3" json:"nickname,omitempty"`
	HeadPortrait      string                 `protobuf:"bytes,7,opt,name=head_portrait,json=headPortrait,proto3" json:"head_portrait,omitempty"`
	Gender            int32                  `protobuf:"varint,8,opt,name=gender,proto3" json:"gender,omitempty"`
	Qq                string                 `protobuf:"bytes,9,opt,name=qq,proto3" json:"qq,omitempty"`
	Email             string                 `protobuf:"bytes,10,opt,name=email,proto3" json:"email,omitempty"`
	Birthday          string                 `protobuf:"bytes,11,opt,name=birthday,proto3" json:"birthday,omitempty"`
	ProvinceId        int64                  `protobuf:"varint,12,opt,name=province_id,json=provinceId,proto3" json:"province_id,omitempty"`
	CityId            int64                  `protobuf:"varint,13,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	AreaId            int64                  `protobuf:"varint,14,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	Address           string                 `protobuf:"bytes,15,opt,name=address,proto3" json:"address,omitempty"`
	Mobile            string                 `protobuf:"bytes,16,opt,name=mobile,proto3" json:"mobile,omitempty"`
	TelNo             string                 `protobuf:"bytes,17,opt,name=tel_no,json=telNo,proto3" json:"tel_no,omitempty"`
	BgImage           string                 `protobuf:"bytes,18,opt,name=bg_image,json=bgImage,proto3" json:"bg_image,omitempty"`
	Description       string                 `protobuf:"bytes,19,opt,name=description,proto3" json:"description,omitempty"`
	Role              int32                  `protobuf:"varint,20,opt,name=role,proto3" json:"role,omitempty"`
	CurrentLevel      int32                  `protobuf:"varint,21,opt,name=current_level,json=currentLevel,proto3" json:"current_level,omitempty"`
	LevelBuyType      int32                  `protobuf:"varint,22,opt,name=level_buy_type,json=levelBuyType,proto3" json:"level_buy_type,omitempty"`
	Pid               int64                  `protobuf:"varint,23,opt,name=pid,proto3" json:"pid,omitempty"`
	CertificationType int32                  `protobuf:"varint,24,opt,name=certification_type,json=certificationType,proto3" json:"certification_type,omitempty"`
	Source            string                 `protobuf:"bytes,25,opt,name=source,proto3" json:"source,omitempty"`
	Status            int32                  `protobuf:"varint,26,opt,name=status,proto3" json:"status,omitempty"`
	RegionId          int64                  `protobuf:"varint,27,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateMemberReq) Reset() {
	*x = CreateMemberReq{}
	mi := &file_member_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMemberReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMemberReq) ProtoMessage() {}

func (x *CreateMemberReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMemberReq.ProtoReflect.Descriptor instead.
func (*CreateMemberReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{5}
}

func (x *CreateMemberReq) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *CreateMemberReq) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *CreateMemberReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CreateMemberReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateMemberReq) GetRealname() string {
	if x != nil {
		return x.Realname
	}
	return ""
}

func (x *CreateMemberReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *CreateMemberReq) GetHeadPortrait() string {
	if x != nil {
		return x.HeadPortrait
	}
	return ""
}

func (x *CreateMemberReq) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *CreateMemberReq) GetQq() string {
	if x != nil {
		return x.Qq
	}
	return ""
}

func (x *CreateMemberReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateMemberReq) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *CreateMemberReq) GetProvinceId() int64 {
	if x != nil {
		return x.ProvinceId
	}
	return 0
}

func (x *CreateMemberReq) GetCityId() int64 {
	if x != nil {
		return x.CityId
	}
	return 0
}

func (x *CreateMemberReq) GetAreaId() int64 {
	if x != nil {
		return x.AreaId
	}
	return 0
}

func (x *CreateMemberReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *CreateMemberReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *CreateMemberReq) GetTelNo() string {
	if x != nil {
		return x.TelNo
	}
	return ""
}

func (x *CreateMemberReq) GetBgImage() string {
	if x != nil {
		return x.BgImage
	}
	return ""
}

func (x *CreateMemberReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateMemberReq) GetRole() int32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *CreateMemberReq) GetCurrentLevel() int32 {
	if x != nil {
		return x.CurrentLevel
	}
	return 0
}

func (x *CreateMemberReq) GetLevelBuyType() int32 {
	if x != nil {
		return x.LevelBuyType
	}
	return 0
}

func (x *CreateMemberReq) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *CreateMemberReq) GetCertificationType() int32 {
	if x != nil {
		return x.CertificationType
	}
	return 0
}

func (x *CreateMemberReq) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *CreateMemberReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CreateMemberReq) GetRegionId() int64 {
	if x != nil {
		return x.RegionId
	}
	return 0
}

type UpdateMemberReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Realname          string                 `protobuf:"bytes,2,opt,name=realname,proto3" json:"realname,omitempty"`
	Nickname          string                 `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	HeadPortrait      string                 `protobuf:"bytes,4,opt,name=head_portrait,json=headPortrait,proto3" json:"head_portrait,omitempty"`
	Gender            int32                  `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`
	Qq                string                 `protobuf:"bytes,6,opt,name=qq,proto3" json:"qq,omitempty"`
	Email             string                 `protobuf:"bytes,7,opt,name=email,proto3" json:"email,omitempty"`
	Birthday          string                 `protobuf:"bytes,8,opt,name=birthday,proto3" json:"birthday,omitempty"`
	ProvinceId        int64                  `protobuf:"varint,9,opt,name=province_id,json=provinceId,proto3" json:"province_id,omitempty"`
	CityId            int64                  `protobuf:"varint,10,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	AreaId            int64                  `protobuf:"varint,11,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	Address           string                 `protobuf:"bytes,12,opt,name=address,proto3" json:"address,omitempty"`
	Mobile            string                 `protobuf:"bytes,13,opt,name=mobile,proto3" json:"mobile,omitempty"`
	TelNo             string                 `protobuf:"bytes,14,opt,name=tel_no,json=telNo,proto3" json:"tel_no,omitempty"`
	BgImage           string                 `protobuf:"bytes,15,opt,name=bg_image,json=bgImage,proto3" json:"bg_image,omitempty"`
	Description       string                 `protobuf:"bytes,16,opt,name=description,proto3" json:"description,omitempty"`
	Role              int32                  `protobuf:"varint,17,opt,name=role,proto3" json:"role,omitempty"`
	CurrentLevel      int32                  `protobuf:"varint,18,opt,name=current_level,json=currentLevel,proto3" json:"current_level,omitempty"`
	LevelBuyType      int32                  `protobuf:"varint,19,opt,name=level_buy_type,json=levelBuyType,proto3" json:"level_buy_type,omitempty"`
	Pid               int64                  `protobuf:"varint,20,opt,name=pid,proto3" json:"pid,omitempty"`
	CertificationType int32                  `protobuf:"varint,21,opt,name=certification_type,json=certificationType,proto3" json:"certification_type,omitempty"`
	Source            string                 `protobuf:"bytes,22,opt,name=source,proto3" json:"source,omitempty"`
	Status            int32                  `protobuf:"varint,23,opt,name=status,proto3" json:"status,omitempty"`
	RegionId          int64                  `protobuf:"varint,24,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateMemberReq) Reset() {
	*x = UpdateMemberReq{}
	mi := &file_member_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMemberReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMemberReq) ProtoMessage() {}

func (x *UpdateMemberReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMemberReq.ProtoReflect.Descriptor instead.
func (*UpdateMemberReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateMemberReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateMemberReq) GetRealname() string {
	if x != nil {
		return x.Realname
	}
	return ""
}

func (x *UpdateMemberReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UpdateMemberReq) GetHeadPortrait() string {
	if x != nil {
		return x.HeadPortrait
	}
	return ""
}

func (x *UpdateMemberReq) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *UpdateMemberReq) GetQq() string {
	if x != nil {
		return x.Qq
	}
	return ""
}

func (x *UpdateMemberReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UpdateMemberReq) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *UpdateMemberReq) GetProvinceId() int64 {
	if x != nil {
		return x.ProvinceId
	}
	return 0
}

func (x *UpdateMemberReq) GetCityId() int64 {
	if x != nil {
		return x.CityId
	}
	return 0
}

func (x *UpdateMemberReq) GetAreaId() int64 {
	if x != nil {
		return x.AreaId
	}
	return 0
}

func (x *UpdateMemberReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UpdateMemberReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UpdateMemberReq) GetTelNo() string {
	if x != nil {
		return x.TelNo
	}
	return ""
}

func (x *UpdateMemberReq) GetBgImage() string {
	if x != nil {
		return x.BgImage
	}
	return ""
}

func (x *UpdateMemberReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateMemberReq) GetRole() int32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *UpdateMemberReq) GetCurrentLevel() int32 {
	if x != nil {
		return x.CurrentLevel
	}
	return 0
}

func (x *UpdateMemberReq) GetLevelBuyType() int32 {
	if x != nil {
		return x.LevelBuyType
	}
	return 0
}

func (x *UpdateMemberReq) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *UpdateMemberReq) GetCertificationType() int32 {
	if x != nil {
		return x.CertificationType
	}
	return 0
}

func (x *UpdateMemberReq) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *UpdateMemberReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UpdateMemberReq) GetRegionId() int64 {
	if x != nil {
		return x.RegionId
	}
	return 0
}

type DeleteMemberReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMemberReq) Reset() {
	*x = DeleteMemberReq{}
	mi := &file_member_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMemberReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMemberReq) ProtoMessage() {}

func (x *DeleteMemberReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMemberReq.ProtoReflect.Descriptor instead.
func (*DeleteMemberReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteMemberReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 标签相关消息
type TagInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MerchantId    int64                  `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId       int64                  `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Title         string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Sort          int32                  `protobuf:"varint,5,opt,name=sort,proto3" json:"sort,omitempty"`
	Status        int32                  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TagInfo) Reset() {
	*x = TagInfo{}
	mi := &file_member_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagInfo) ProtoMessage() {}

func (x *TagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagInfo.ProtoReflect.Descriptor instead.
func (*TagInfo) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{8}
}

func (x *TagInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TagInfo) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *TagInfo) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *TagInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *TagInfo) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *TagInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *TagInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *TagInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type GetTagReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTagReq) Reset() {
	*x = GetTagReq{}
	mi := &file_member_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTagReq) ProtoMessage() {}

func (x *GetTagReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTagReq.ProtoReflect.Descriptor instead.
func (*GetTagReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{9}
}

func (x *GetTagReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListTagsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    int64                  `protobuf:"varint,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId       int64                  `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Keyword       string                 `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	Page          int32                  `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTagsReq) Reset() {
	*x = ListTagsReq{}
	mi := &file_member_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTagsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTagsReq) ProtoMessage() {}

func (x *ListTagsReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTagsReq.ProtoReflect.Descriptor instead.
func (*ListTagsReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{10}
}

func (x *ListTagsReq) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *ListTagsReq) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *ListTagsReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *ListTagsReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListTagsReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTagsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListTagsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*TagInfo             `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTagsResp) Reset() {
	*x = ListTagsResp{}
	mi := &file_member_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTagsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTagsResp) ProtoMessage() {}

func (x *ListTagsResp) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTagsResp.ProtoReflect.Descriptor instead.
func (*ListTagsResp) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{11}
}

func (x *ListTagsResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListTagsResp) GetList() []*TagInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type CreateTagReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    int64                  `protobuf:"varint,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId       int64                  `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Sort          int32                  `protobuf:"varint,4,opt,name=sort,proto3" json:"sort,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTagReq) Reset() {
	*x = CreateTagReq{}
	mi := &file_member_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTagReq) ProtoMessage() {}

func (x *CreateTagReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTagReq.ProtoReflect.Descriptor instead.
func (*CreateTagReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{12}
}

func (x *CreateTagReq) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *CreateTagReq) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *CreateTagReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateTagReq) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *CreateTagReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type UpdateTagReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Sort          int32                  `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTagReq) Reset() {
	*x = UpdateTagReq{}
	mi := &file_member_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTagReq) ProtoMessage() {}

func (x *UpdateTagReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTagReq.ProtoReflect.Descriptor instead.
func (*UpdateTagReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateTagReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTagReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateTagReq) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *UpdateTagReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type DeleteTagReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTagReq) Reset() {
	*x = DeleteTagReq{}
	mi := &file_member_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTagReq) ProtoMessage() {}

func (x *DeleteTagReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTagReq.ProtoReflect.Descriptor instead.
func (*DeleteTagReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteTagReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 会员标签关联相关消息
type AddTagsToMemberReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	TagIds        []int64                `protobuf:"varint,2,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddTagsToMemberReq) Reset() {
	*x = AddTagsToMemberReq{}
	mi := &file_member_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTagsToMemberReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTagsToMemberReq) ProtoMessage() {}

func (x *AddTagsToMemberReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTagsToMemberReq.ProtoReflect.Descriptor instead.
func (*AddTagsToMemberReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{15}
}

func (x *AddTagsToMemberReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *AddTagsToMemberReq) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

type RemoveTagsFromMemberReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	TagIds        []int64                `protobuf:"varint,2,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveTagsFromMemberReq) Reset() {
	*x = RemoveTagsFromMemberReq{}
	mi := &file_member_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveTagsFromMemberReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveTagsFromMemberReq) ProtoMessage() {}

func (x *RemoveTagsFromMemberReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveTagsFromMemberReq.ProtoReflect.Descriptor instead.
func (*RemoveTagsFromMemberReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{16}
}

func (x *RemoveTagsFromMemberReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *RemoveTagsFromMemberReq) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

type GetMemberTagsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMemberTagsReq) Reset() {
	*x = GetMemberTagsReq{}
	mi := &file_member_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMemberTagsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMemberTagsReq) ProtoMessage() {}

func (x *GetMemberTagsReq) ProtoReflect() protoreflect.Message {
	mi := &file_member_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMemberTagsReq.ProtoReflect.Descriptor instead.
func (*GetMemberTagsReq) Descriptor() ([]byte, []int) {
	return file_member_proto_rawDescGZIP(), []int{17}
}

func (x *GetMemberTagsReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

var File_member_proto protoreflect.FileDescriptor

const file_member_proto_rawDesc = "" +
	"\n" +
	"\fmember.proto\x12\x06member\"@\n" +
	"\n" +
	"CommonResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x89\b\n" +
	"\n" +
	"MemberInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vmerchant_id\x18\x02 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x03 \x01(\x03R\astoreId\x12\x1a\n" +
	"\busername\x18\x04 \x01(\tR\busername\x12\x1a\n" +
	"\brealname\x18\x05 \x01(\tR\brealname\x12\x1a\n" +
	"\bnickname\x18\x06 \x01(\tR\bnickname\x12#\n" +
	"\rhead_portrait\x18\a \x01(\tR\fheadPortrait\x12\x16\n" +
	"\x06gender\x18\b \x01(\x05R\x06gender\x12\x0e\n" +
	"\x02qq\x18\t \x01(\tR\x02qq\x12\x14\n" +
	"\x05email\x18\n" +
	" \x01(\tR\x05email\x12\x1a\n" +
	"\bbirthday\x18\v \x01(\tR\bbirthday\x12\x1f\n" +
	"\vprovince_id\x18\f \x01(\x03R\n" +
	"provinceId\x12\x17\n" +
	"\acity_id\x18\r \x01(\x03R\x06cityId\x12\x17\n" +
	"\aarea_id\x18\x0e \x01(\x03R\x06areaId\x12\x18\n" +
	"\aaddress\x18\x0f \x01(\tR\aaddress\x12\x16\n" +
	"\x06mobile\x18\x10 \x01(\tR\x06mobile\x12\x15\n" +
	"\x06tel_no\x18\x11 \x01(\tR\x05telNo\x12\x19\n" +
	"\bbg_image\x18\x12 \x01(\tR\abgImage\x12 \n" +
	"\vdescription\x18\x13 \x01(\tR\vdescription\x12\x1f\n" +
	"\vvisit_count\x18\x14 \x01(\x05R\n" +
	"visitCount\x12\x1b\n" +
	"\tlast_time\x18\x15 \x01(\x03R\blastTime\x12\x17\n" +
	"\alast_ip\x18\x16 \x01(\tR\x06lastIp\x12\x12\n" +
	"\x04role\x18\x17 \x01(\x05R\x04role\x12#\n" +
	"\rcurrent_level\x18\x18 \x01(\x05R\fcurrentLevel\x122\n" +
	"\x15level_expiration_time\x18\x19 \x01(\x03R\x13levelExpirationTime\x12$\n" +
	"\x0elevel_buy_type\x18\x1a \x01(\x05R\flevelBuyType\x12\x10\n" +
	"\x03pid\x18\x1b \x01(\x03R\x03pid\x12\x14\n" +
	"\x05level\x18\x1c \x01(\x05R\x05level\x12\x12\n" +
	"\x04tree\x18\x1d \x01(\tR\x04tree\x12#\n" +
	"\rpromoter_code\x18\x1e \x01(\tR\fpromoterCode\x12-\n" +
	"\x12certification_type\x18\x1f \x01(\x05R\x11certificationType\x12\x16\n" +
	"\x06source\x18  \x01(\tR\x06source\x12\x16\n" +
	"\x06status\x18! \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\" \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18# \x01(\x03R\tupdatedAt\x12\x1b\n" +
	"\tregion_id\x18$ \x01(\x03R\bregionId\"\x1e\n" +
	"\fGetMemberReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xaf\x01\n" +
	"\x0eListMembersReq\x12\x1f\n" +
	"\vmerchant_id\x18\x01 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x02 \x01(\x03R\astoreId\x12\x18\n" +
	"\akeyword\x18\x03 \x01(\tR\akeyword\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\x12\x12\n" +
	"\x04page\x18\x05 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x06 \x01(\x05R\bpageSize\"O\n" +
	"\x0fListMembersResp\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12&\n" +
	"\x04list\x18\x02 \x03(\v2\x12.member.MemberInfoR\x04list\"\x82\x06\n" +
	"\x0fCreateMemberReq\x12\x1f\n" +
	"\vmerchant_id\x18\x01 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x02 \x01(\x03R\astoreId\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\x12\x1a\n" +
	"\brealname\x18\x05 \x01(\tR\brealname\x12\x1a\n" +
	"\bnickname\x18\x06 \x01(\tR\bnickname\x12#\n" +
	"\rhead_portrait\x18\a \x01(\tR\fheadPortrait\x12\x16\n" +
	"\x06gender\x18\b \x01(\x05R\x06gender\x12\x0e\n" +
	"\x02qq\x18\t \x01(\tR\x02qq\x12\x14\n" +
	"\x05email\x18\n" +
	" \x01(\tR\x05email\x12\x1a\n" +
	"\bbirthday\x18\v \x01(\tR\bbirthday\x12\x1f\n" +
	"\vprovince_id\x18\f \x01(\x03R\n" +
	"provinceId\x12\x17\n" +
	"\acity_id\x18\r \x01(\x03R\x06cityId\x12\x17\n" +
	"\aarea_id\x18\x0e \x01(\x03R\x06areaId\x12\x18\n" +
	"\aaddress\x18\x0f \x01(\tR\aaddress\x12\x16\n" +
	"\x06mobile\x18\x10 \x01(\tR\x06mobile\x12\x15\n" +
	"\x06tel_no\x18\x11 \x01(\tR\x05telNo\x12\x19\n" +
	"\bbg_image\x18\x12 \x01(\tR\abgImage\x12 \n" +
	"\vdescription\x18\x13 \x01(\tR\vdescription\x12\x12\n" +
	"\x04role\x18\x14 \x01(\x05R\x04role\x12#\n" +
	"\rcurrent_level\x18\x15 \x01(\x05R\fcurrentLevel\x12$\n" +
	"\x0elevel_buy_type\x18\x16 \x01(\x05R\flevelBuyType\x12\x10\n" +
	"\x03pid\x18\x17 \x01(\x03R\x03pid\x12-\n" +
	"\x12certification_type\x18\x18 \x01(\x05R\x11certificationType\x12\x16\n" +
	"\x06source\x18\x19 \x01(\tR\x06source\x12\x16\n" +
	"\x06status\x18\x1a \x01(\x05R\x06status\x12\x1b\n" +
	"\tregion_id\x18\x1b \x01(\x03R\bregionId\"\x9e\x05\n" +
	"\x0fUpdateMemberReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1a\n" +
	"\brealname\x18\x02 \x01(\tR\brealname\x12\x1a\n" +
	"\bnickname\x18\x03 \x01(\tR\bnickname\x12#\n" +
	"\rhead_portrait\x18\x04 \x01(\tR\fheadPortrait\x12\x16\n" +
	"\x06gender\x18\x05 \x01(\x05R\x06gender\x12\x0e\n" +
	"\x02qq\x18\x06 \x01(\tR\x02qq\x12\x14\n" +
	"\x05email\x18\a \x01(\tR\x05email\x12\x1a\n" +
	"\bbirthday\x18\b \x01(\tR\bbirthday\x12\x1f\n" +
	"\vprovince_id\x18\t \x01(\x03R\n" +
	"provinceId\x12\x17\n" +
	"\acity_id\x18\n" +
	" \x01(\x03R\x06cityId\x12\x17\n" +
	"\aarea_id\x18\v \x01(\x03R\x06areaId\x12\x18\n" +
	"\aaddress\x18\f \x01(\tR\aaddress\x12\x16\n" +
	"\x06mobile\x18\r \x01(\tR\x06mobile\x12\x15\n" +
	"\x06tel_no\x18\x0e \x01(\tR\x05telNo\x12\x19\n" +
	"\bbg_image\x18\x0f \x01(\tR\abgImage\x12 \n" +
	"\vdescription\x18\x10 \x01(\tR\vdescription\x12\x12\n" +
	"\x04role\x18\x11 \x01(\x05R\x04role\x12#\n" +
	"\rcurrent_level\x18\x12 \x01(\x05R\fcurrentLevel\x12$\n" +
	"\x0elevel_buy_type\x18\x13 \x01(\x05R\flevelBuyType\x12\x10\n" +
	"\x03pid\x18\x14 \x01(\x03R\x03pid\x12-\n" +
	"\x12certification_type\x18\x15 \x01(\x05R\x11certificationType\x12\x16\n" +
	"\x06source\x18\x16 \x01(\tR\x06source\x12\x16\n" +
	"\x06status\x18\x17 \x01(\x05R\x06status\x12\x1b\n" +
	"\tregion_id\x18\x18 \x01(\x03R\bregionId\"!\n" +
	"\x0fDeleteMemberReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xd5\x01\n" +
	"\aTagInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vmerchant_id\x18\x02 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x03 \x01(\x03R\astoreId\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x12\n" +
	"\x04sort\x18\x05 \x01(\x05R\x04sort\x12\x16\n" +
	"\x06status\x18\x06 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\b \x01(\x03R\tupdatedAt\"\x1b\n" +
	"\tGetTagReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xac\x01\n" +
	"\vListTagsReq\x12\x1f\n" +
	"\vmerchant_id\x18\x01 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x02 \x01(\x03R\astoreId\x12\x18\n" +
	"\akeyword\x18\x03 \x01(\tR\akeyword\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\x12\x12\n" +
	"\x04page\x18\x05 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x06 \x01(\x05R\bpageSize\"I\n" +
	"\fListTagsResp\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12#\n" +
	"\x04list\x18\x02 \x03(\v2\x0f.member.TagInfoR\x04list\"\x8c\x01\n" +
	"\fCreateTagReq\x12\x1f\n" +
	"\vmerchant_id\x18\x01 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x02 \x01(\x03R\astoreId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x12\n" +
	"\x04sort\x18\x04 \x01(\x05R\x04sort\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\"`\n" +
	"\fUpdateTagReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\"\x1e\n" +
	"\fDeleteTagReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"J\n" +
	"\x12AddTagsToMemberReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x17\n" +
	"\atag_ids\x18\x02 \x03(\x03R\x06tagIds\"O\n" +
	"\x17RemoveTagsFromMemberReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x17\n" +
	"\atag_ids\x18\x02 \x03(\x03R\x06tagIds\"/\n" +
	"\x10GetMemberTagsReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId2\x8b\x06\n" +
	"\x06Member\x125\n" +
	"\tGetMember\x12\x14.member.GetMemberReq\x1a\x12.member.MemberInfo\x12>\n" +
	"\vListMembers\x12\x16.member.ListMembersReq\x1a\x17.member.ListMembersResp\x12;\n" +
	"\fCreateMember\x12\x17.member.CreateMemberReq\x1a\x12.member.MemberInfo\x12;\n" +
	"\fUpdateMember\x12\x17.member.UpdateMemberReq\x1a\x12.member.MemberInfo\x12;\n" +
	"\fDeleteMember\x12\x17.member.DeleteMemberReq\x1a\x12.member.CommonResp\x12,\n" +
	"\x06GetTag\x12\x11.member.GetTagReq\x1a\x0f.member.TagInfo\x125\n" +
	"\bListTags\x12\x13.member.ListTagsReq\x1a\x14.member.ListTagsResp\x122\n" +
	"\tCreateTag\x12\x14.member.CreateTagReq\x1a\x0f.member.TagInfo\x122\n" +
	"\tUpdateTag\x12\x14.member.UpdateTagReq\x1a\x0f.member.TagInfo\x125\n" +
	"\tDeleteTag\x12\x14.member.DeleteTagReq\x1a\x12.member.CommonResp\x12A\n" +
	"\x0fAddTagsToMember\x12\x1a.member.AddTagsToMemberReq\x1a\x12.member.CommonResp\x12K\n" +
	"\x14RemoveTagsFromMember\x12\x1f.member.RemoveTagsFromMemberReq\x1a\x12.member.CommonResp\x12?\n" +
	"\rGetMemberTags\x12\x18.member.GetMemberTagsReq\x1a\x14.member.ListTagsRespB\n" +
	"Z\b./memberb\x06proto3"

var (
	file_member_proto_rawDescOnce sync.Once
	file_member_proto_rawDescData []byte
)

func file_member_proto_rawDescGZIP() []byte {
	file_member_proto_rawDescOnce.Do(func() {
		file_member_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_member_proto_rawDesc), len(file_member_proto_rawDesc)))
	})
	return file_member_proto_rawDescData
}

var file_member_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_member_proto_goTypes = []any{
	(*CommonResp)(nil),              // 0: member.CommonResp
	(*MemberInfo)(nil),              // 1: member.MemberInfo
	(*GetMemberReq)(nil),            // 2: member.GetMemberReq
	(*ListMembersReq)(nil),          // 3: member.ListMembersReq
	(*ListMembersResp)(nil),         // 4: member.ListMembersResp
	(*CreateMemberReq)(nil),         // 5: member.CreateMemberReq
	(*UpdateMemberReq)(nil),         // 6: member.UpdateMemberReq
	(*DeleteMemberReq)(nil),         // 7: member.DeleteMemberReq
	(*TagInfo)(nil),                 // 8: member.TagInfo
	(*GetTagReq)(nil),               // 9: member.GetTagReq
	(*ListTagsReq)(nil),             // 10: member.ListTagsReq
	(*ListTagsResp)(nil),            // 11: member.ListTagsResp
	(*CreateTagReq)(nil),            // 12: member.CreateTagReq
	(*UpdateTagReq)(nil),            // 13: member.UpdateTagReq
	(*DeleteTagReq)(nil),            // 14: member.DeleteTagReq
	(*AddTagsToMemberReq)(nil),      // 15: member.AddTagsToMemberReq
	(*RemoveTagsFromMemberReq)(nil), // 16: member.RemoveTagsFromMemberReq
	(*GetMemberTagsReq)(nil),        // 17: member.GetMemberTagsReq
}
var file_member_proto_depIdxs = []int32{
	1,  // 0: member.ListMembersResp.list:type_name -> member.MemberInfo
	8,  // 1: member.ListTagsResp.list:type_name -> member.TagInfo
	2,  // 2: member.Member.GetMember:input_type -> member.GetMemberReq
	3,  // 3: member.Member.ListMembers:input_type -> member.ListMembersReq
	5,  // 4: member.Member.CreateMember:input_type -> member.CreateMemberReq
	6,  // 5: member.Member.UpdateMember:input_type -> member.UpdateMemberReq
	7,  // 6: member.Member.DeleteMember:input_type -> member.DeleteMemberReq
	9,  // 7: member.Member.GetTag:input_type -> member.GetTagReq
	10, // 8: member.Member.ListTags:input_type -> member.ListTagsReq
	12, // 9: member.Member.CreateTag:input_type -> member.CreateTagReq
	13, // 10: member.Member.UpdateTag:input_type -> member.UpdateTagReq
	14, // 11: member.Member.DeleteTag:input_type -> member.DeleteTagReq
	15, // 12: member.Member.AddTagsToMember:input_type -> member.AddTagsToMemberReq
	16, // 13: member.Member.RemoveTagsFromMember:input_type -> member.RemoveTagsFromMemberReq
	17, // 14: member.Member.GetMemberTags:input_type -> member.GetMemberTagsReq
	1,  // 15: member.Member.GetMember:output_type -> member.MemberInfo
	4,  // 16: member.Member.ListMembers:output_type -> member.ListMembersResp
	1,  // 17: member.Member.CreateMember:output_type -> member.MemberInfo
	1,  // 18: member.Member.UpdateMember:output_type -> member.MemberInfo
	0,  // 19: member.Member.DeleteMember:output_type -> member.CommonResp
	8,  // 20: member.Member.GetTag:output_type -> member.TagInfo
	11, // 21: member.Member.ListTags:output_type -> member.ListTagsResp
	8,  // 22: member.Member.CreateTag:output_type -> member.TagInfo
	8,  // 23: member.Member.UpdateTag:output_type -> member.TagInfo
	0,  // 24: member.Member.DeleteTag:output_type -> member.CommonResp
	0,  // 25: member.Member.AddTagsToMember:output_type -> member.CommonResp
	0,  // 26: member.Member.RemoveTagsFromMember:output_type -> member.CommonResp
	11, // 27: member.Member.GetMemberTags:output_type -> member.ListTagsResp
	15, // [15:28] is the sub-list for method output_type
	2,  // [2:15] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_member_proto_init() }
func file_member_proto_init() {
	if File_member_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_member_proto_rawDesc), len(file_member_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_member_proto_goTypes,
		DependencyIndexes: file_member_proto_depIdxs,
		MessageInfos:      file_member_proto_msgTypes,
	}.Build()
	File_member_proto = out.File
	file_member_proto_goTypes = nil
	file_member_proto_depIdxs = nil
}
