package member

import (
	"context"
	"fmt"
	"mp/rpc/model"
	"time"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateMemberLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateMemberLogic {
	return &UpdateMemberLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UpdateMemberLogic) UpdateMember(in *member.UpdateMemberReq) (*member.MemberInfo, error) {
	// todo: add your logic here and delete this line

	// 检查ID是否有效
	if in.Id < 0 {
		return nil, fmt.Errorf("无效的ID: %d", in.Id)
	}

	// 查询记录是否存在
	var memberData model.Member
	result := l.svcCtx.DB.Where("id = ?", in.Id).First(&memberData)

	if result.Error != nil {
		return nil, fmt.Errorf("查询会员失败: %v", result.Error)
	}

	// 处理生日字段的转换
	var birthday *time.Time
	if in.Birthday != "" {
		// 尝试解析生日字符串，假设格式为 "2006-01-02"
		if parsedTime, err := time.Parse("2006-01-02", in.Birthday); err == nil {
			birthday = &parsedTime
		} else {
			l.Errorf("解析生日字段失败: %v", err)
		}
	}

	// 构建更新数据
	updateData := &model.Member{
		Realname:          in.Realname,
		Nickname:          in.Nickname,
		HeadPortrait:      in.HeadPortrait,
		Gender:            uint8(in.Gender),
		QQ:                in.Qq,
		Email:             in.Email,
		Birthday:          birthday,
		ProvinceID:        uint(in.ProvinceId),
		CityID:            uint(in.CityId),
		AreaID:            uint(in.AreaId),
		Address:           in.Address,
		Mobile:            in.Mobile,
		TelNo:             in.TelNo,
		BgImage:           in.BgImage,
		Description:       in.Description,
		Role:              int16(in.Role),
		CurrentLevel:      int8(in.CurrentLevel),
		LevelBuyType:      int8(in.LevelBuyType),
		Pid:               uint(in.Pid),
		CertificationType: int8(in.CertificationType),
		Source:            in.Source,
		Status:            int8(in.Status),
		RegionID:          uint(in.RegionId),
	}
	result = l.svcCtx.DB.Where("id=?", in.Id).Updates(&updateData)

	if result.Error != nil {
		l.Errorf("更新会员信息失败: %v", result.Error)
		return nil, result.Error
	}

	// 根据id查询数据库 获取最新的数据
	result = l.svcCtx.DB.First(&memberData, in.Id)
	if result.Error != nil {
		l.Errorf("查找会员信息失败: %v", result.Error)
		return nil, result.Error
	}

	return &member.MemberInfo{
		Id:                  in.Id,
		MerchantId:          int64(memberData.MerchantID),
		StoreId:             int64(memberData.StoreID),
		Username:            memberData.Username,
		Realname:            memberData.Realname,
		Nickname:            memberData.Nickname,
		HeadPortrait:        memberData.HeadPortrait,
		Gender:              int32(memberData.Gender),
		Qq:                  memberData.QQ,
		Email:               memberData.Email,
		Birthday:            in.Birthday,
		ProvinceId:          int64(memberData.ProvinceID),
		CityId:              int64(memberData.CityID),
		AreaId:              int64(memberData.AreaID),
		Address:             memberData.Address,
		Mobile:              memberData.Mobile,
		TelNo:               memberData.TelNo,
		BgImage:             memberData.BgImage,
		Description:         memberData.Description,
		VisitCount:          int32(memberData.VisitCount),
		LastTime:            int64(memberData.LastTime),
		LastIp:              memberData.LastIP,
		Role:                int32(memberData.Role),
		CurrentLevel:        int32(memberData.CurrentLevel),
		LevelExpirationTime: int64(memberData.LevelExpirationTime),
		LevelBuyType:        int32(memberData.LevelBuyType),
		Pid:                 int64(memberData.Pid),
		Level:               int32(memberData.Level),
		Tree:                memberData.Tree,
		PromoterCode:        memberData.PromoterCode,
		CertificationType:   int32(memberData.CertificationType),
		Source:              memberData.Source,
		Status:              int32(memberData.Status),
		CreatedAt:           int64(memberData.CreatedAt),
		UpdatedAt:           int64(memberData.UpdatedAt),
		RegionId:            int64(memberData.RegionID),
	}, nil
}
