-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- 导出会员标签相关表

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `mp_db`
--

-- --------------------------------------------------------

--
-- 表的结构 `member_tag`
--

CREATE TABLE `member_tag` (
  `id` int(11) NOT NULL COMMENT '主键',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态',
  `created_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_标签表';

-- --------------------------------------------------------

--
-- 表的结构 `member_tag_map`
--

CREATE TABLE `member_tag_map` (
  `tag_id` int(11) DEFAULT '0' COMMENT '标签id',
  `member_id` int(11) DEFAULT '0' COMMENT '文章id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_标签关联表';

-- --------------------------------------------------------

--
-- 表的结构 `member`
--

CREATE TABLE `member` (
  `id` int(11) NOT NULL,
  `merchant_id` int(11) DEFAULT '0' COMMENT '商户ID',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `username` varchar(20) NOT NULL DEFAULT '' COMMENT '账号',
  `password_hash` varchar(150) NOT NULL DEFAULT '' COMMENT '密码',
  `auth_key` varchar(32) NOT NULL DEFAULT '' COMMENT '授权令牌',
  `password_reset_token` varchar(150) DEFAULT '' COMMENT '密码重置令牌',
  `mobile_reset_token` varchar(150) DEFAULT '' COMMENT '手机号码重置令牌',
  `type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `realname` varchar(50) DEFAULT '' COMMENT '真实姓名',
  `nickname` varchar(60) DEFAULT '' COMMENT '昵称',
  `head_portrait` char(150) DEFAULT '' COMMENT '头像',
  `gender` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '性别[0:未知;1:男;2:女]',
  `qq` varchar(20) DEFAULT '' COMMENT 'qq',
  `email` varchar(60) DEFAULT '' COMMENT '邮箱',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `province_id` int(11) DEFAULT '0' COMMENT '省',
  `city_id` int(11) DEFAULT '0' COMMENT '城市',
  `area_id` int(11) DEFAULT '0' COMMENT '地区',
  `address` varchar(100) DEFAULT '' COMMENT '默认地址',
  `mobile` varchar(20) DEFAULT '' COMMENT '手机号码',
  `tel_no` varchar(20) DEFAULT '' COMMENT '电话号码',
  `bg_image` varchar(200) DEFAULT '' COMMENT '个人背景图',
  `description` varchar(200) DEFAULT '' COMMENT '个人说明',
  `visit_count` smallint(5) UNSIGNED DEFAULT '0' COMMENT '访问次数',
  `last_time` int(11) DEFAULT '0' COMMENT '最后一次登录时间',
  `last_ip` varchar(40) DEFAULT '' COMMENT '最后一次登录ip',
  `role` smallint(6) DEFAULT '10' COMMENT '权限',
  `current_level` tinyint(4) DEFAULT '1' COMMENT '当前级别',
  `level_expiration_time` int(11) DEFAULT '0' COMMENT '等级到期时间',
  `level_buy_type` tinyint(4) DEFAULT '1' COMMENT '1:赠送;2:购买',
  `pid` int(10) UNSIGNED DEFAULT '0' COMMENT '上级id',
  `level` tinyint(3) UNSIGNED DEFAULT '1' COMMENT '级别',
  `tree` varchar(2000) DEFAULT '' COMMENT '树',
  `promoter_code` varchar(50) DEFAULT '' COMMENT '推广码',
  `certification_type` tinyint(4) DEFAULT '0' COMMENT '认证类型',
  `source` varchar(50) DEFAULT '' COMMENT '注册来源',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间',
  `region_id` int(11) DEFAULT '0' COMMENT '数据权限'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员表';

--
-- 转储表的索引
--

--
-- 表的索引 `member`
--
ALTER TABLE `member`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `member_tag`
--
ALTER TABLE `member_tag`
  ADD PRIMARY KEY (`id`),
  ADD KEY `tag_id` (`id`);

--
-- 表的索引 `member_tag_map`
--
ALTER TABLE `member_tag_map`
  ADD KEY `tag_id` (`tag_id`),
  ADD KEY `article_id` (`member_id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `member`
--
ALTER TABLE `member`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `member_tag`
--
ALTER TABLE `member_tag`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键';

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */; 