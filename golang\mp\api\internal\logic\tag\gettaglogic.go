package tag

import (
	"context"
	"fmt"
	"mp/rpc/member"

	"mp/api/internal/svc"
	"mp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取标签详情
func NewGetTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTagLogic {
	return &GetTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTagLogic) GetTag(req *types.GetTagReq) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	rpcResp, err := l.svcCtx.MemberRpc.GetTag(l.ctx, &member.GetTagReq{
		Id: req.Id,
	})
	if err != nil {
		return nil, fmt.Errorf("调用 RPC GetTag 服务 失败: %v ", err)
	}
	return &types.CommonResponse{
		Code:    200,
		Message: "调用 RPC GetTag 服务 成功",
		Data:    rpcResp,
	}, nil
}
