package tag

import (
	"net/http"

	"mp/api/internal/logic/tag"
	"mp/api/internal/svc"
	"mp/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 获取标签详情
func GetTagHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetTagReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := tag.NewGetTagLogic(r.Context(), svcCtx)
		resp, err := l.GetTag(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
