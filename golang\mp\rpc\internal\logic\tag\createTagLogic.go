package tag

import (
	"context"
	"fmt"
	"mp/rpc/internal/svc"
	"mp/rpc/member"
	"mp/rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateTagLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateTagLogic {
	return &CreateTagLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreateTagLogic) CreateTag(in *member.CreateTagReq) (*member.TagInfo, error) {
	// 检查必要的参数
	if in.MerchantId == 0 {
		return nil, fmt.Errorf("商户ID不能为空")
	}

	if in.Title == "" {
		return nil, fmt.Errorf("标签标题不能为空")
	}

	// 创建标签数据
	tagData := &model.MemberTag{
		MerchantID: uint(in.MerchantId),
		StoreID:    uint(in.StoreId),
		Title:      in.Title,
		Sort:       int(in.Sort),
		Status:     int8(in.Status),
	}

	// 保存到数据库
	if err := l.svcCtx.DB.Create(tagData).Error; err != nil {
		return nil, fmt.Errorf("创建标签失败: %v", err)
	}

	// 返回创建的标签信息
	return &member.TagInfo{
		Id:         int64(tagData.ID),
		MerchantId: int64(tagData.MerchantID),
		StoreId:    int64(tagData.StoreID),
		Title:      tagData.Title,
		Sort:       int32(tagData.Sort),
		Status:     int32(tagData.Status),
		CreatedAt:  int64(tagData.CreatedAt),
		UpdatedAt:  int64(tagData.UpdatedAt),
	}, nil
}
