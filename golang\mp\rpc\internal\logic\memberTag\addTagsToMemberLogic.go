package memberTag

import (
	"context"
	"fmt"
	"mp/rpc/model"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddTagsToMemberLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddTagsToMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddTagsToMemberLogic {
	return &AddTagsToMemberLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// CommonResp 定义一个本地的CommonResp结构体
type CommonResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// 会员标签关联
func (l *AddTagsToMemberLogic) AddTagsToMember(in *member.AddTagsToMemberReq) (interface{}, error) {
	// 添加日志
	l.Infof("接收到添加会员标签请求: %+v", in)

	// 检查DB是否为nil
	if l.svcCtx.DB == nil {
		return nil, fmt.Errorf("数据库连接为空，请检查数据库配置")
	}

	// 检查必要字段
	if in.MemberId == 0 {
		return nil, fmt.Errorf("会员ID不能为空")
	}
	if len(in.TagIds) == 0 {
		return nil, fmt.Errorf("标签ID不能为空")
	}

	// 开启事务
	tx := l.svcCtx.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 先检查会员是否存在
	var member model.Member
	if err := tx.First(&member, uint(in.MemberId)).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("会员不存在: %v", err)
	}

	// 遍历所有标签ID，创建关联
	for _, tagID := range in.TagIds {
		// 检查标签是否存在
		var tag model.MemberTag
		if err := tx.First(&tag, uint(tagID)).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("标签ID %d 不存在: %v", tagID, err)
		}

		// 检查关联是否已存在
		var count int64
		tx.Model(&model.MemberTagMap{}).
			Where("member_id = ? AND tag_id = ?", in.MemberId, tagID).
			Count(&count)

		// 如果关联已存在，跳过
		if count > 0 {
			continue
		}

		// 创建新的关联
		memberTag := &model.MemberTagMap{
			TagID:    uint(tagID),
			MemberID: uint(in.MemberId),
		}
		if err := tx.Create(memberTag).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("添加会员标签关联失败: %v", err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 返回结果
	return &CommonResp{
		Success: true,
		Message: "添加会员标签成功，共添加" + fmt.Sprintf("%d", len(in.TagIds)) + "个标签",
	}, nil
}
