// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.0
// source: member.proto

package member

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Member_GetMember_FullMethodName            = "/member.Member/GetMember"
	Member_ListMembers_FullMethodName          = "/member.Member/ListMembers"
	Member_CreateMember_FullMethodName         = "/member.Member/CreateMember"
	Member_UpdateMember_FullMethodName         = "/member.Member/UpdateMember"
	Member_DeleteMember_FullMethodName         = "/member.Member/DeleteMember"
	Member_GetTag_FullMethodName               = "/member.Member/GetTag"
	Member_ListTags_FullMethodName             = "/member.Member/ListTags"
	Member_CreateTag_FullMethodName            = "/member.Member/CreateTag"
	Member_UpdateTag_FullMethodName            = "/member.Member/UpdateTag"
	Member_DeleteTag_FullMethodName            = "/member.Member/DeleteTag"
	Member_AddTagsToMember_FullMethodName      = "/member.Member/AddTagsToMember"
	Member_RemoveTagsFromMember_FullMethodName = "/member.Member/RemoveTagsFromMember"
	Member_GetMemberTags_FullMethodName        = "/member.Member/GetMemberTags"
)

// MemberClient is the client API for Member service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 会员服务定义
type MemberClient interface {
	// 会员管理
	GetMember(ctx context.Context, in *GetMemberReq, opts ...grpc.CallOption) (*MemberInfo, error)
	ListMembers(ctx context.Context, in *ListMembersReq, opts ...grpc.CallOption) (*ListMembersResp, error)
	CreateMember(ctx context.Context, in *CreateMemberReq, opts ...grpc.CallOption) (*MemberInfo, error)
	UpdateMember(ctx context.Context, in *UpdateMemberReq, opts ...grpc.CallOption) (*MemberInfo, error)
	DeleteMember(ctx context.Context, in *DeleteMemberReq, opts ...grpc.CallOption) (*CommonResp, error)
	// 标签管理
	GetTag(ctx context.Context, in *GetTagReq, opts ...grpc.CallOption) (*TagInfo, error)
	ListTags(ctx context.Context, in *ListTagsReq, opts ...grpc.CallOption) (*ListTagsResp, error)
	CreateTag(ctx context.Context, in *CreateTagReq, opts ...grpc.CallOption) (*TagInfo, error)
	UpdateTag(ctx context.Context, in *UpdateTagReq, opts ...grpc.CallOption) (*TagInfo, error)
	DeleteTag(ctx context.Context, in *DeleteTagReq, opts ...grpc.CallOption) (*CommonResp, error)
	// 会员标签关联
	AddTagsToMember(ctx context.Context, in *AddTagsToMemberReq, opts ...grpc.CallOption) (*CommonResp, error)
	RemoveTagsFromMember(ctx context.Context, in *RemoveTagsFromMemberReq, opts ...grpc.CallOption) (*CommonResp, error)
	GetMemberTags(ctx context.Context, in *GetMemberTagsReq, opts ...grpc.CallOption) (*ListTagsResp, error)
}

type memberClient struct {
	cc grpc.ClientConnInterface
}

func NewMemberClient(cc grpc.ClientConnInterface) MemberClient {
	return &memberClient{cc}
}

func (c *memberClient) GetMember(ctx context.Context, in *GetMemberReq, opts ...grpc.CallOption) (*MemberInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MemberInfo)
	err := c.cc.Invoke(ctx, Member_GetMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) ListMembers(ctx context.Context, in *ListMembersReq, opts ...grpc.CallOption) (*ListMembersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListMembersResp)
	err := c.cc.Invoke(ctx, Member_ListMembers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) CreateMember(ctx context.Context, in *CreateMemberReq, opts ...grpc.CallOption) (*MemberInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MemberInfo)
	err := c.cc.Invoke(ctx, Member_CreateMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) UpdateMember(ctx context.Context, in *UpdateMemberReq, opts ...grpc.CallOption) (*MemberInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MemberInfo)
	err := c.cc.Invoke(ctx, Member_UpdateMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) DeleteMember(ctx context.Context, in *DeleteMemberReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, Member_DeleteMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) GetTag(ctx context.Context, in *GetTagReq, opts ...grpc.CallOption) (*TagInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TagInfo)
	err := c.cc.Invoke(ctx, Member_GetTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) ListTags(ctx context.Context, in *ListTagsReq, opts ...grpc.CallOption) (*ListTagsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTagsResp)
	err := c.cc.Invoke(ctx, Member_ListTags_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) CreateTag(ctx context.Context, in *CreateTagReq, opts ...grpc.CallOption) (*TagInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TagInfo)
	err := c.cc.Invoke(ctx, Member_CreateTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) UpdateTag(ctx context.Context, in *UpdateTagReq, opts ...grpc.CallOption) (*TagInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TagInfo)
	err := c.cc.Invoke(ctx, Member_UpdateTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) DeleteTag(ctx context.Context, in *DeleteTagReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, Member_DeleteTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) AddTagsToMember(ctx context.Context, in *AddTagsToMemberReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, Member_AddTagsToMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) RemoveTagsFromMember(ctx context.Context, in *RemoveTagsFromMemberReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, Member_RemoveTagsFromMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberClient) GetMemberTags(ctx context.Context, in *GetMemberTagsReq, opts ...grpc.CallOption) (*ListTagsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTagsResp)
	err := c.cc.Invoke(ctx, Member_GetMemberTags_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MemberServer is the server API for Member service.
// All implementations must embed UnimplementedMemberServer
// for forward compatibility.
//
// 会员服务定义
type MemberServer interface {
	// 会员管理
	GetMember(context.Context, *GetMemberReq) (*MemberInfo, error)
	ListMembers(context.Context, *ListMembersReq) (*ListMembersResp, error)
	CreateMember(context.Context, *CreateMemberReq) (*MemberInfo, error)
	UpdateMember(context.Context, *UpdateMemberReq) (*MemberInfo, error)
	DeleteMember(context.Context, *DeleteMemberReq) (*CommonResp, error)
	// 标签管理
	GetTag(context.Context, *GetTagReq) (*TagInfo, error)
	ListTags(context.Context, *ListTagsReq) (*ListTagsResp, error)
	CreateTag(context.Context, *CreateTagReq) (*TagInfo, error)
	UpdateTag(context.Context, *UpdateTagReq) (*TagInfo, error)
	DeleteTag(context.Context, *DeleteTagReq) (*CommonResp, error)
	// 会员标签关联
	AddTagsToMember(context.Context, *AddTagsToMemberReq) (*CommonResp, error)
	RemoveTagsFromMember(context.Context, *RemoveTagsFromMemberReq) (*CommonResp, error)
	GetMemberTags(context.Context, *GetMemberTagsReq) (*ListTagsResp, error)
	mustEmbedUnimplementedMemberServer()
}

// UnimplementedMemberServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMemberServer struct{}

func (UnimplementedMemberServer) GetMember(context.Context, *GetMemberReq) (*MemberInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMember not implemented")
}
func (UnimplementedMemberServer) ListMembers(context.Context, *ListMembersReq) (*ListMembersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMembers not implemented")
}
func (UnimplementedMemberServer) CreateMember(context.Context, *CreateMemberReq) (*MemberInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMember not implemented")
}
func (UnimplementedMemberServer) UpdateMember(context.Context, *UpdateMemberReq) (*MemberInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMember not implemented")
}
func (UnimplementedMemberServer) DeleteMember(context.Context, *DeleteMemberReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMember not implemented")
}
func (UnimplementedMemberServer) GetTag(context.Context, *GetTagReq) (*TagInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTag not implemented")
}
func (UnimplementedMemberServer) ListTags(context.Context, *ListTagsReq) (*ListTagsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTags not implemented")
}
func (UnimplementedMemberServer) CreateTag(context.Context, *CreateTagReq) (*TagInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTag not implemented")
}
func (UnimplementedMemberServer) UpdateTag(context.Context, *UpdateTagReq) (*TagInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTag not implemented")
}
func (UnimplementedMemberServer) DeleteTag(context.Context, *DeleteTagReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTag not implemented")
}
func (UnimplementedMemberServer) AddTagsToMember(context.Context, *AddTagsToMemberReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTagsToMember not implemented")
}
func (UnimplementedMemberServer) RemoveTagsFromMember(context.Context, *RemoveTagsFromMemberReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveTagsFromMember not implemented")
}
func (UnimplementedMemberServer) GetMemberTags(context.Context, *GetMemberTagsReq) (*ListTagsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMemberTags not implemented")
}
func (UnimplementedMemberServer) mustEmbedUnimplementedMemberServer() {}
func (UnimplementedMemberServer) testEmbeddedByValue()                {}

// UnsafeMemberServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MemberServer will
// result in compilation errors.
type UnsafeMemberServer interface {
	mustEmbedUnimplementedMemberServer()
}

func RegisterMemberServer(s grpc.ServiceRegistrar, srv MemberServer) {
	// If the following call pancis, it indicates UnimplementedMemberServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Member_ServiceDesc, srv)
}

func _Member_GetMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).GetMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_GetMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).GetMember(ctx, req.(*GetMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_ListMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).ListMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_ListMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).ListMembers(ctx, req.(*ListMembersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_CreateMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).CreateMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_CreateMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).CreateMember(ctx, req.(*CreateMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_UpdateMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).UpdateMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_UpdateMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).UpdateMember(ctx, req.(*UpdateMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_DeleteMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).DeleteMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_DeleteMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).DeleteMember(ctx, req.(*DeleteMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_GetTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).GetTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_GetTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).GetTag(ctx, req.(*GetTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_ListTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).ListTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_ListTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).ListTags(ctx, req.(*ListTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_CreateTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).CreateTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_CreateTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).CreateTag(ctx, req.(*CreateTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_UpdateTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).UpdateTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_UpdateTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).UpdateTag(ctx, req.(*UpdateTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_DeleteTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).DeleteTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_DeleteTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).DeleteTag(ctx, req.(*DeleteTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_AddTagsToMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTagsToMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).AddTagsToMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_AddTagsToMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).AddTagsToMember(ctx, req.(*AddTagsToMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_RemoveTagsFromMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveTagsFromMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).RemoveTagsFromMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_RemoveTagsFromMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).RemoveTagsFromMember(ctx, req.(*RemoveTagsFromMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Member_GetMemberTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServer).GetMemberTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Member_GetMemberTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServer).GetMemberTags(ctx, req.(*GetMemberTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Member_ServiceDesc is the grpc.ServiceDesc for Member service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Member_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "member.Member",
	HandlerType: (*MemberServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMember",
			Handler:    _Member_GetMember_Handler,
		},
		{
			MethodName: "ListMembers",
			Handler:    _Member_ListMembers_Handler,
		},
		{
			MethodName: "CreateMember",
			Handler:    _Member_CreateMember_Handler,
		},
		{
			MethodName: "UpdateMember",
			Handler:    _Member_UpdateMember_Handler,
		},
		{
			MethodName: "DeleteMember",
			Handler:    _Member_DeleteMember_Handler,
		},
		{
			MethodName: "GetTag",
			Handler:    _Member_GetTag_Handler,
		},
		{
			MethodName: "ListTags",
			Handler:    _Member_ListTags_Handler,
		},
		{
			MethodName: "CreateTag",
			Handler:    _Member_CreateTag_Handler,
		},
		{
			MethodName: "UpdateTag",
			Handler:    _Member_UpdateTag_Handler,
		},
		{
			MethodName: "DeleteTag",
			Handler:    _Member_DeleteTag_Handler,
		},
		{
			MethodName: "AddTagsToMember",
			Handler:    _Member_AddTagsToMember_Handler,
		},
		{
			MethodName: "RemoveTagsFromMember",
			Handler:    _Member_RemoveTagsFromMember_Handler,
		},
		{
			MethodName: "GetMemberTags",
			Handler:    _Member_GetMemberTags_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "member.proto",
}
