package model

import (
	"gorm.io/gorm"
)

type MemberTagMap struct {
	TagID    uint `gorm:"type:int;column:tag_id;comment:标签id;primaryKey" json:"tag_id"`
	MemberID uint `gorm:"type:int;column:member_id;comment:会员id;primaryKey" json:"member_id"`
}

// 指定生成的表名
func (MemberTagMap) TableName() string {
	return "member_tag_map"
}

// 批量获取会员的标签
func GetMembersWithTags(db *gorm.DB, memberIDs []uint) (map[uint][]MemberTag, error) {
	result := make(map[uint][]MemberTag)

	// 查询所有关联记录
	var maps []MemberTagMap
	if err := db.Where("member_id IN ?", memberIDs).Find(&maps).Error; err != nil {
		return nil, err
	}

	// 如果没有关联记录，返回空结果
	if len(maps) == 0 {
		return result, nil
	}

	// 提取所有标签ID
	var tagIDs []uint
	memberTagMap := make(map[uint][]uint) // 会员ID -> 标签ID列表
	for _, m := range maps {
		tagIDs = append(tagIDs, m.TagID)
		memberTagMap[m.MemberID] = append(memberTagMap[m.MemberID], m.TagID)
	}

	// 查询所有标签信息
	var tags []MemberTag
	if err := db.Where("id IN ?", tagIDs).Find(&tags).Error; err != nil {
		return nil, err
	}

	// 创建标签ID到标签对象的映射
	tagMap := make(map[uint]MemberTag)
	for _, tag := range tags {
		tagMap[tag.ID] = tag
	}

	// 构建最终结果
	for memberID, tagIDs := range memberTagMap {
		memberTags := make([]MemberTag, 0, len(tagIDs))
		for _, tagID := range tagIDs {
			if tag, exists := tagMap[tagID]; exists {
				memberTags = append(memberTags, tag)
			}
		}
		result[memberID] = memberTags
	}

	return result, nil
}

// 批量获取标签的会员
func GetTagsWithMembers(db *gorm.DB, tagIDs []uint) (map[uint][]Member, error) {
	result := make(map[uint][]Member)

	// 查询所有关联记录
	var maps []MemberTagMap
	if err := db.Where("tag_id IN ?", tagIDs).Find(&maps).Error; err != nil {
		return nil, err
	}

	// 如果没有关联记录，返回空结果
	if len(maps) == 0 {
		return result, nil
	}

	// 提取所有会员ID
	var memberIDs []uint
	tagMemberMap := make(map[uint][]uint) // 标签ID -> 会员ID列表
	for _, m := range maps {
		memberIDs = append(memberIDs, m.MemberID)
		tagMemberMap[m.TagID] = append(tagMemberMap[m.TagID], m.MemberID)
	}

	// 查询所有会员信息
	var members []Member
	if err := db.Where("id IN ?", memberIDs).Find(&members).Error; err != nil {
		return nil, err
	}

	// 创建会员ID到会员对象的映射
	memberMap := make(map[uint]Member)
	for _, member := range members {
		memberMap[member.ID] = member
	}

	// 构建最终结果
	for tagID, memberIDs := range tagMemberMap {
		tagMembers := make([]Member, 0, len(memberIDs))
		for _, memberID := range memberIDs {
			if member, exists := memberMap[memberID]; exists {
				tagMembers = append(tagMembers, member)
			}
		}
		result[tagID] = tagMembers
	}

	return result, nil
}
