package member

import (
	"context"
	"fmt"
	"mp/rpc/internal/svc"
	"mp/rpc/member"
	"mp/rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMemberLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberLogic {
	return &GetMemberLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 会员管理
func (l *GetMemberLogic) GetMember(in *member.GetMemberReq) (*member.MemberInfo, error) {
	// todo: add your logic here and delete this line

	// 检查ID是否有效
	if in.Id < 0 {
		return nil, fmt.Errorf("无效的ID: %d", in.Id)
	}

	var memberData model.Member
	result := l.svcCtx.DB.First(&memberData, in.Id)

	if result.Error != nil {
		return nil, fmt.Errorf("查询数据失败: %v", result.Error)
	}

	// 如果没有找到该数据
	if result.RowsAffected == 0 {
		return nil, fmt.Errorf("未找到ID为%d的会员记录", in.Id)
	}

	// 处理生日字段的转换
	var birthday string
	if memberData.Birthday == nil {
		birthday = ""

	} else {
		birthday = memberData.Birthday.Format("2006-01-02")
	}

	//// 将查询结果转换为响应格式
	//memberResp := &member.MemberInfo{
	//	Id:                  int64(memberData.ID),
	//	MerchantId:          int64(memberData.MerchantID),
	//	StoreId:             int64(memberData.StoreID),
	//	Username:            memberData.Username,
	//	Realname:            memberData.Realname,
	//	Nickname:            memberData.Nickname,
	//	HeadPortrait:        memberData.HeadPortrait,
	//	Gender:              int32(memberData.Gender),
	//	Qq:                  memberData.QQ,
	//	Email:               memberData.Email,
	//	Birthday:            birthday,
	//	ProvinceId:          int64(memberData.ProvinceID),
	//	CityId:              int64(memberData.CityID),
	//	AreaId:              int64(memberData.AreaID),
	//	Address:             memberData.Address,
	//	Mobile:              memberData.Mobile,
	//	TelNo:               memberData.TelNo,
	//	BgImage:             memberData.BgImage,
	//	Description:         memberData.Description,
	//	VisitCount:          int32(memberData.VisitCount),
	//	LastTime:            int64(memberData.LastTime),
	//	LastIp:              memberData.LastIP,
	//	Role:                int32(memberData.Role),
	//	CurrentLevel:        int32(memberData.CurrentLevel),
	//	LevelExpirationTime: int64(memberData.LevelExpirationTime),
	//	LevelBuyType:        int32(memberData.LevelBuyType),
	//	Pid:                 int64(memberData.Pid),
	//	Level:               int32(memberData.Level),
	//	Tree:                memberData.Tree,
	//	PromoterCode:        memberData.PromoterCode,
	//	CertificationType:   int32(memberData.CertificationType),
	//	Source:              memberData.Source,
	//	Status:              int32(memberData.Status),
	//	CreatedAt:           int64(memberData.CreatedAt),
	//	UpdatedAt:           int64(memberData.UpdatedAt),
	//	RegionId:            int64(memberData.RegionID),
	//}

	return &member.MemberInfo{
		Id:                  int64(memberData.ID),
		MerchantId:          int64(memberData.MerchantID),
		StoreId:             int64(memberData.StoreID),
		Username:            memberData.Username,
		Realname:            memberData.Realname,
		Nickname:            memberData.Nickname,
		HeadPortrait:        memberData.HeadPortrait,
		Gender:              int32(memberData.Gender),
		Qq:                  memberData.QQ,
		Email:               memberData.Email,
		Birthday:            birthday,
		ProvinceId:          int64(memberData.ProvinceID),
		CityId:              int64(memberData.CityID),
		AreaId:              int64(memberData.AreaID),
		Address:             memberData.Address,
		Mobile:              memberData.Mobile,
		TelNo:               memberData.TelNo,
		BgImage:             memberData.BgImage,
		Description:         memberData.Description,
		VisitCount:          int32(memberData.VisitCount),
		LastTime:            int64(memberData.LastTime),
		LastIp:              memberData.LastIP,
		Role:                int32(memberData.Role),
		CurrentLevel:        int32(memberData.CurrentLevel),
		LevelExpirationTime: int64(memberData.LevelExpirationTime),
		LevelBuyType:        int32(memberData.LevelBuyType),
		Pid:                 int64(memberData.Pid),
		Level:               int32(memberData.Level),
		Tree:                memberData.Tree,
		PromoterCode:        memberData.PromoterCode,
		CertificationType:   int32(memberData.CertificationType),
		Source:              memberData.Source,
		Status:              int32(memberData.Status),
		CreatedAt:           int64(memberData.CreatedAt),
		UpdatedAt:           int64(memberData.UpdatedAt),
		RegionId:            int64(memberData.RegionID),
	}, nil
}
