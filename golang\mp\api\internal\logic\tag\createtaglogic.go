package tag

import (
	"context"
	"fmt"
	"mp/rpc/member"

	"mp/api/internal/svc"
	"mp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建标签
func NewCreateTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateTagLogic {
	return &CreateTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateTagLogic) CreateTag(req *types.CreateTagReq) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	rpcResp, err := l.svcCtx.MemberRpc.CreateTag(l.ctx, &member.CreateTagReq{
		MerchantId: req.MerchantId,
		StoreId:    req.StoreId,
		Title:      req.Title,
		Sort:       int32(req.Sort),
		Status:     int32(req.Status),
	})
	if err != nil {
		return nil, fmt.Errorf("调用 RPC CreateTag 服务 失败 %v", err)
	}
	return &types.CommonResponse{
		Code:    200,
		Message: "调用 RPC CreateTag 服务 成功",
		Data:    rpcResp,
	}, nil
}
