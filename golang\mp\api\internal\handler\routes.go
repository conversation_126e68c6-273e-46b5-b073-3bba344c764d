// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package handler

import (
	"net/http"

	member "mp/api/internal/handler/member"
	memberTag "mp/api/internal/handler/memberTag"
	tag "mp/api/internal/handler/tag"
	"mp/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// 会员列表
				Method:  http.MethodGet,
				Path:    "/",
				Handler: member.ListMembersHandler(serverCtx),
			},
			{
				// 创建会员
				Method:  http.MethodPost,
				Path:    "/",
				Handler: member.CreateMemberHandler(serverCtx),
			},
			{
				// 获取会员详情
				Method:  http.MethodGet,
				Path:    "/:id",
				Handler: member.GetMemberHandler(serverCtx),
			},
			{
				// 更新会员
				Method:  http.MethodPut,
				Path:    "/:id",
				Handler: member.UpdateMemberHandler(serverCtx),
			},
			{
				// 删除会员
				Method:  http.MethodDelete,
				Path:    "/:id",
				Handler: member.DeleteMemberHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/members"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 为会员添加标签
				Method:  http.MethodPost,
				Path:    "/",
				Handler: memberTag.AddTagsToMemberHandler(serverCtx),
			},
			{
				// 移除会员标签
				Method:  http.MethodDelete,
				Path:    "/",
				Handler: memberTag.RemoveTagsFromMemberHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/memberTags"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 标签列表
				Method:  http.MethodGet,
				Path:    "/",
				Handler: tag.ListTagsHandler(serverCtx),
			},
			{
				// 创建标签
				Method:  http.MethodPost,
				Path:    "/",
				Handler: tag.CreateTagHandler(serverCtx),
			},
			{
				// 获取标签详情
				Method:  http.MethodGet,
				Path:    "/:id",
				Handler: tag.GetTagHandler(serverCtx),
			},
			{
				// 更新标签
				Method:  http.MethodPut,
				Path:    "/:id",
				Handler: tag.UpdateTagHandler(serverCtx),
			},
			{
				// 删除标签
				Method:  http.MethodDelete,
				Path:    "/:id",
				Handler: tag.DeleteTagHandler(serverCtx),
			},
			{
				// 获取会员标签
				Method:  http.MethodGet,
				Path:    "/member/:memberId",
				Handler: tag.GetMemberTagsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/tags"),
	)
}
