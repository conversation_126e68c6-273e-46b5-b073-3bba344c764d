FROM golang:alpine AS builder

LABEL stage=gobuilder

ENV CGO_ENABLED 0
# 使用多个代理源，提高成功率
ENV GOPROXY=https://goproxy.io,https://goproxy.cn,https://proxy.golang.org,direct
ENV GOSUMDB=sum.golang.org
ENV GO111MODULE=on

# 安装必要工具和CA证书
RUN apk update --no-cache && apk add --no-cache tzdata ca-certificates git

WORKDIR /build

# 先复制go.mod和go.sum，利用Docker缓存层（从构建上下文根目录）
COPY go.mod go.sum ./

# 下载依赖，增加重试机制
RUN go mod download || go mod download || go mod download

# 复制所有源代码文件（从构建上下文根目录）
COPY . .

# 确认文件已复制（调试用）
RUN ls -la && find . -name "*.go" | head -10

# 构建API应用（指定API目录下的main文件）
RUN go build -ldflags="-s -w" -o /app/member ./api


FROM scratch

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ Asia/Shanghai

WORKDIR /app
COPY --from=builder /app/member /app/member
COPY --from=builder /build/api/etc /app/etc

CMD ["./member", "-f", "etc/member-api.yaml"]
