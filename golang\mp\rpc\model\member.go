package model

import (
	"time"

	"gorm.io/gorm"
)

type Member struct {
	ID                  uint       `gorm:"primaryKey;type:int;column:id" json:"id"`
	MerchantID          uint       `gorm:"type:int;column:merchant_id;comment:商户ID" json:"merchantId"`
	StoreID             uint       `gorm:"type:int unsigned;column:store_id;comment:店铺ID" json:"storeId"`
	Username            string     `gorm:"type:varchar(20);column:username;not null;comment:账号" json:"username"`
	PasswordHash        string     `gorm:"type:varchar(150);column:password_hash;not null;comment:密码" json:"passwordHash,omitempty"`
	AuthKey             string     `gorm:"type:varchar(32);column:auth_key;not null;comment:授权令牌" json:"authKey,omitempty"`
	PasswordResetToken  string     `gorm:"type:varchar(150);column:password_reset_token;comment:密码重置令牌" json:"passwordResetToken,omitempty"`
	MobileResetToken    string     `gorm:"type:varchar(150);column:mobile_reset_token;comment:手机号码重置令牌" json:"mobileResetToken,omitempty"`
	Type                int8       `gorm:"type:tinyint(4);column:type;comment:1:会员;2:后台管理员;3:商家管理员" json:"type,omitempty"`
	Realname            string     `gorm:"type:varchar(50);column:realname;comment:真实姓名" json:"realname"`
	Nickname            string     `gorm:"type:varchar(60);column:nickname;comment:昵称" json:"nickname"`
	HeadPortrait        string     `gorm:"type:char(150);column:head_portrait;comment:头像" json:"headPortrait"`
	Gender              uint8      `gorm:"type:tinyint(3) unsigned;column:gender;comment:性别[0:未知;1:男;2:女]" json:"gender"`
	QQ                  string     `gorm:"type:varchar(20);column:qq;comment:qq" json:"qq"`
	Email               string     `gorm:"type:varchar(60);column:email;comment:邮箱" json:"email"`
	Birthday            *time.Time `gorm:"type:date;column:birthday;comment:生日" json:"birthday"`
	ProvinceID          uint       `gorm:"type:int;column:province_id;comment:省" json:"provinceId"`
	CityID              uint       `gorm:"type:int;column:city_id;comment:城市" json:"cityId"`
	AreaID              uint       `gorm:"type:int;column:area_id;comment:地区" json:"areaId"`
	Address             string     `gorm:"type:varchar(100);column:address;comment:默认地址" json:"address"`
	Mobile              string     `gorm:"type:varchar(20);column:mobile;comment:手机号码" json:"mobile"`
	TelNo               string     `gorm:"type:varchar(20);column:tel_no;comment:电话号码" json:"telNo"`
	BgImage             string     `gorm:"type:varchar(200);column:bg_image;comment:个人背景图" json:"bgImage"`
	Description         string     `gorm:"type:varchar(200);column:description;comment:个人说明" json:"description"`
	VisitCount          uint16     `gorm:"type:smallint(5) unsigned;column:visit_count;comment:访问次数" json:"visitCount"`
	LastTime            int        `gorm:"type:int;column:last_time;comment:最后一次登录时间" json:"lastTime"`
	LastIP              string     `gorm:"type:varchar(40);column:last_ip;comment:最后一次登录ip" json:"lastIp"`
	Role                int16      `gorm:"type:smallint(6);column:role;comment:权限" json:"role"`
	CurrentLevel        int8       `gorm:"type:tinyint(4);column:current_level;comment:当前级别" json:"currentLevel"`
	LevelExpirationTime int        `gorm:"type:int;column:level_expiration_time;comment:等级到期时间" json:"levelExpirationTime"`
	LevelBuyType        int8       `gorm:"type:tinyint(4);column:level_buy_type;comment:1:赠送;2:购买" json:"levelBuyType"`
	Pid                 uint       `gorm:"type:int unsigned;column:pid;comment:上级id" json:"pid"`
	Level               uint8      `gorm:"type:tinyint(3) unsigned;column:level;comment:级别" json:"level"`
	Tree                string     `gorm:"type:varchar(2000);column:tree;comment:树" json:"tree"`
	PromoterCode        string     `gorm:"type:varchar(50);column:promoter_code;comment:推广码" json:"promoterCode"`
	CertificationType   int8       `gorm:"type:tinyint(4);column:certification_type;comment:认证类型" json:"certificationType"`
	Source              string     `gorm:"type:varchar(50);column:source;comment:注册来源" json:"source"`
	Status              int8       `gorm:"type:tinyint(4);column:status;comment:状态[-1:删除;0:禁用;1启用]" json:"status"`
	CreatedAt           uint       `gorm:"type:int unsigned;column:created_at;comment:创建时间" json:"createdAt"`
	UpdatedAt           uint       `gorm:"type:int unsigned;column:updated_at;comment:修改时间" json:"updatedAt"`
	RegionID            uint       `gorm:"type:int;column:region_id;comment:数据权限" json:"regionId"`

	// 多对多关系: 会员拥有多个标签
	Tags []MemberTag `gorm:"many2many:member_tag_map;foreignKey:ID;joinForeignKey:MemberID;References:ID;joinReferences:TagID" json:"tags,omitempty"`
}

// 指定生成的表名
func (Member) TableName() string {
	return "member"
}

// 添加标签到会员
func (m *Member) AddTags(db *gorm.DB, tagIDs []uint) error {
	// 开启事务
	tx := db.Begin()

	// 获取会员现有标签
	var existingTagMaps []MemberTagMap
	if err := tx.Where("member_id = ?", m.ID).Find(&existingTagMaps).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 创建现有标签ID的map，方便查找
	existingTagIDMap := make(map[uint]bool)
	for _, tagMap := range existingTagMaps {
		existingTagIDMap[tagMap.TagID] = true
	}

	// 添加新标签关联
	for _, tagID := range tagIDs {
		// 如果标签已存在，跳过
		if existingTagIDMap[tagID] {
			continue
		}

		// 创建新关联
		if err := tx.Create(&MemberTagMap{
			MemberID: m.ID,
			TagID:    tagID,
		}).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// 移除会员的标签
func (m *Member) RemoveTags(db *gorm.DB, tagIDs []uint) error {
	return db.Where("member_id = ? AND tag_id IN ?", m.ID, tagIDs).Delete(&MemberTagMap{}).Error
}

// 获取会员的所有标签
func (m *Member) GetTags(db *gorm.DB) ([]MemberTag, error) {
	var tags []MemberTag
	err := db.Model(m).Association("Tags").Find(&tags)
	return tags, err
}
