package logic

import (
	"context"
	"fmt"

	"user/rpc/internal/svc"
	"user/rpc/middleware/JWT"
	"user/rpc/model"
	"user/rpc/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRegisterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterLogic {
	return &RegisterLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// Register creates a new user
func (l *RegisterLogic) Register(in *user.RegisterRequest) (*user.UserResponse, error) {
	// 在注册之前先检查该用户是否已经注册过
	existUser, err := l.svcCtx.UserModel.FindByUsername(in.Username)
	if err == nil && existUser != nil {
		return nil, fmt.Errorf("用户名已存在，请登录")
	}

	// 用户名不存在开始进行注册用户
	userData := &model.User{
		Username: in.Username,
		Password: in.Password,
		Email:    in.Email,
		Phone:    in.Phone,
	}

	// 使用gorm框架进行数据库操作
	err = l.svcCtx.UserModel.CreateUser(userData, l.svcCtx.Config.Auth.AccessSecret)

	if err != nil {
		return nil, fmt.Errorf("注册失败，请重新注册: %v", err)
	}

	newUser, err := l.svcCtx.UserModel.FindByUsername(in.Username)
	if err != nil {
		return nil, fmt.Errorf("查询新用户失败: %v", err)
	}

	token, err := JWT.GetJwtToken(l.svcCtx.Config.Auth.AccessSecret, 0, l.svcCtx.Config.Auth.AccessExpire, int(newUser.ID))
	if err != nil {
		return nil, fmt.Errorf("生成token失败: %v", err)
	}

	// 返回数据给api服务
	return &user.UserResponse{
		Id:       newUser.ID,
		Username: newUser.Username,
		Email:    newUser.Email,
		Phone:    newUser.Phone,
		Token:    token,
		Code:     200,
		Message:  "注册成功",
	}, nil
}
