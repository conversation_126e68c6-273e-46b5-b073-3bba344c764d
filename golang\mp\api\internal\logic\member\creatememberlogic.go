package member

import (
	"context"
	"fmt"
	"mp/rpc/member"

	"mp/api/internal/svc"
	"mp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateMemberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建会员
func NewCreateMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateMemberLogic {
	return &CreateMemberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateMemberLogic) CreateMember(req *types.CreateMemberReq) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	rpcResp, err := l.svcCtx.MemberRpc.CreateMember(l.ctx, &member.CreateMemberReq{
		MerchantId:        req.MerchantId,
		StoreId:           req.StoreId,
		Username:          req.Username,
		Password:          req.Password,
		Realname:          req.Realname,
		Nickname:          req.Nickname,
		HeadPortrait:      req.HeadPortrait,
		Gender:            int32(req.Gender),
		Qq:                req.Qq,
		Email:             req.Email,
		Birthday:          req.Birthday,
		ProvinceId:        req.ProvinceId,
		CityId:            req.CityId,
		AreaId:            req.AreaId,
		Address:           req.Address,
		Mobile:            req.Mobile,
		TelNo:             req.TelNo,
		BgImage:           req.BgImage,
		Description:       req.Description,
		Role:              int32(req.Role),
		CurrentLevel:      int32(req.CurrentLevel),
		LevelBuyType:      int32(req.LevelBuyType),
		Pid:               req.Pid,
		CertificationType: int32(req.CertificationType),
		Source:            req.Source,
		Status:            int32(req.Status),
		RegionId:          req.RegionId,
	})

	if err != nil {
		return nil, fmt.Errorf("调用 RPC CreateMember 服务 失败 %v", err)
	}

	return &types.CommonResponse{
		Code:    200,
		Message: "调用 RPC CreateMember 服务 成功",
		Data:    rpcResp,
	}, nil

}
