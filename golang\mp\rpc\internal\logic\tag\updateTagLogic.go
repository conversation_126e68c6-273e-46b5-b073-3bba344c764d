package tag

import (
	"context"
	"fmt"
	"mp/rpc/internal/svc"
	"mp/rpc/member"
	"mp/rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateTagLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateTagLogic {
	return &UpdateTagLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UpdateTagLogic) UpdateTag(in *member.UpdateTagReq) (*member.TagInfo, error) {
	// todo: add your logic here and delete this line

	// 检查ID是否有效
	if in.Id < 0 {
		return nil, fmt.Errorf("无效的ID: %d", in.Id)
	}

	// 查询该标签是否存在数据库中
	var tagData model.MemberTag
	result := l.svcCtx.DB.Where("id = ?", in.Id).First(&tagData)

	if result.Error != nil {
		return nil, fmt.Errorf("查询数据库失败: %v", result.Error)
	}
	// 更新标签信息
	updataData := &model.MemberTag{
		Title:  in.Title,
		Sort:   int(in.Sort),
		Status: int8(in.Status),
	}

	// 根据id查询数据库 获取最新的数据
	result = l.svcCtx.DB.First(&tagData, in.Id)
	if result.Error != nil {
		l.Errorf("查找会员信息失败: %v", result.Error)
		return nil, result.Error
	}

	return &member.TagInfo{
		Id:         int64(tagData.ID),
		MerchantId: int64(tagData.MerchantID),
		StoreId:    int64(tagData.StoreID),
		Title:      updataData.Title,
		Sort:       int32(updataData.Sort),
		Status:     int32(updataData.Status),
		CreatedAt:  int64(updataData.CreatedAt),
		UpdatedAt:  int64(tagData.UpdatedAt),
	}, nil
}
