package tag

import (
	"context"
	"fmt"
	"strconv"

	"mp/rpc/model"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteTagLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteTagLogic {
	return &DeleteTagLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeleteTagLogic) DeleteTag(in *member.DeleteTagReq) (*member.CommonResp, error) {
	// todo: add your logic here and delete this line

	var existingMember model.MemberTag
	result := l.svcCtx.DB.First(&existingMember, in.Id)

	if result.Error != nil {
		return nil, fmt.Errorf("查询该标签失败: %v", result.Error)
	}

	// 如果没有找到该标签
	if result.RowsAffected == 0 {
		return &member.CommonResp{
			Success: true,
			Message: "数据库中为未找到该数据",
		}, fmt.Errorf("未找到ID为%d的会员记录", in.Id)
	}

	// 找到记录并删除数据
	result = l.svcCtx.DB.Where("id=?", in.Id).Delete(&existingMember)
	if result.Error != nil {
		return &member.CommonResp{
			Success: false,
			Message: "删除该记录 " + strconv.Itoa(int(in.Id)) + " 失败",
		}, fmt.Errorf("删除标签失败: %v", result.Error)
	}

	return &member.CommonResp{
		Success: true,
		Message: "删除该记录 " + strconv.Itoa(int(in.Id)) + " 成功",
	}, nil
}
