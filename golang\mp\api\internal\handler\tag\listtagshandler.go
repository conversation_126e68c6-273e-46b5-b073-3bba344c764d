package tag

import (
	"net/http"

	"mp/api/internal/logic/tag"
	"mp/api/internal/svc"
	"mp/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 标签列表
func ListTagsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ListTagsReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := tag.NewListTagsLogic(r.Context(), svcCtx)
		resp, err := l.ListTags(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
