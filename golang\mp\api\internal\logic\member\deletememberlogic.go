package member

import (
	"context"
	"fmt"
	"mp/rpc/member"

	"mp/api/internal/svc"
	"mp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteMemberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除会员
func NewDeleteMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteMemberLogic {
	return &DeleteMemberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteMemberLogic) DeleteMember(req *types.DeleteMemberReq) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	_, err = l.svcCtx.MemberRpc.DeleteMember(l.ctx, &member.DeleteMemberReq{
		Id: req.Id,
	})

	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("调用 RPC DeleteMember 服务 失败 %v", err)
	}

	return &types.CommonResponse{
		Code:    200,
		Message: "调用 RPC DeleteMember 服务 成功",
	}, nil
}
