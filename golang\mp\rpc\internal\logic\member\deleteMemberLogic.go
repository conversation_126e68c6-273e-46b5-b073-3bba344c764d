package member

import (
	"context"
	"fmt"
	"mp/rpc/model"
	"strconv"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteMemberLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteMemberLogic {
	return &DeleteMemberLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeleteMemberLogic) DeleteMember(in *member.DeleteMemberReq) (*member.CommonResp, error) {
	// todo: add your logic here and delete this line

	// 先查询是否存在该记录
	var existingMember model.Member
	result := l.svcCtx.DB.First(&existingMember, in.Id)
	if result.Error != nil {
		return nil, fmt.Errorf("查询数据失败: %v", result.Error)
	}

	// 如果没有查询到数据
	if result.RowsAffected == 0 {
		return &member.CommonResp{
			Success: true,
			Message: "数据库中未找到该数据",
		}, fmt.Errorf("未找到ID为%d的会员记录", in.Id)
	}

	// 执行删除操作
	result = l.svcCtx.DB.Where("id=?", in.Id).Delete(&existingMember)
	if result.Error != nil {
		return &member.CommonResp{
			Success: false,
			Message: "删除该记录 " + strconv.Itoa(int(in.Id)) + " 失败",
		}, fmt.Errorf("删除数据失败: %v", result.Error)
	}

	return &member.CommonResp{
		Success: true,
		Message: "该记录 " + strconv.Itoa(int(in.Id)) + " 删除成功",
	}, nil
}
