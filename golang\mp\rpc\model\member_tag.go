package model

import (
	"gorm.io/gorm"
)

type MemberTag struct {
	ID         uint   `gorm:"primaryKey;type:int;column:id;comment:主键" json:"id"`
	MerchantID uint   `gorm:"type:int unsigned;column:merchant_id;comment:商户id" json:"merchant_id"`
	StoreID    uint   `gorm:"type:int unsigned;column:store_id;comment:店铺ID" json:"store_id"`
	Title      string `gorm:"type:varchar(50);column:title;not null;comment:标题" json:"title"`
	Sort       int    `gorm:"type:int;column:sort;comment:排序" json:"sort"`
	Status     int8   `gorm:"type:tinyint(4);column:status;comment:状态" json:"status"`
	CreatedAt  uint   `gorm:"type:int unsigned;column:created_at;not null;comment:创建时间" json:"created_at"`
	UpdatedAt  uint   `gorm:"type:int unsigned;column:updated_at;not null;comment:更新时间" json:"updated_at"`

	// 多对多关系: 标签被多个会员拥有
	Members []Member `gorm:"many2many:member_tag_map;foreignKey:ID;joinForeignKey:TagID;References:ID;joinReferences:MemberID" json:"members,omitempty"`
}

// 指定生成的表名
func (MemberTag) TableName() string {
	return "member_tag"
}

// 获取拥有此标签的所有会员
func (t *MemberTag) GetMembers(db *gorm.DB) ([]Member, error) {
	var members []Member
	err := db.Model(t).Association("Members").Find(&members)
	return members, err
}

// 添加会员到标签
func (t *MemberTag) AddMembers(db *gorm.DB, memberIDs []uint) error {
	// 开启事务
	tx := db.Begin()

	// 获取标签现有会员
	var existingMemberMaps []MemberTagMap
	if err := tx.Where("tag_id = ?", t.ID).Find(&existingMemberMaps).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 创建现有会员ID的map，方便查找
	existingMemberIDMap := make(map[uint]bool)
	for _, memberMap := range existingMemberMaps {
		existingMemberIDMap[memberMap.MemberID] = true
	}

	// 添加新会员关联
	for _, memberID := range memberIDs {
		// 如果会员已存在，跳过
		if existingMemberIDMap[memberID] {
			continue
		}

		// 创建新关联
		if err := tx.Create(&MemberTagMap{
			TagID:    t.ID,
			MemberID: memberID,
		}).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 提交事务
	return tx.Commit().Error
}
