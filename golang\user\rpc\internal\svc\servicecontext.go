package svc

import (
	"user/rpc/internal/config"
	"user/rpc/model"

	"mp/rpc/memberclient"

	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config    config.Config
	UserModel model.UserModel
	MemberRpc memberclient.Member
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:    c,
		UserModel: *model.NewUserModel(model.NewDb(c.MySQL.DataSource)),
		MemberRpc: memberclient.NewMember(zrpc.MustNewClient(c.MemberRpc)),
	}
}
