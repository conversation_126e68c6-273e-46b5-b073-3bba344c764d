package memberTag

import (
	"context"
	"fmt"
	"mp/rpc/model"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMemberTagsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetMemberTagsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberTagsLogic {
	return &GetMemberTagsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetMemberTagsLogic) GetMemberTags(in *member.GetMemberTagsReq) (*member.ListTagsResp, error) {
	// 检查是否有效
	if in.MemberId <= 0 {
		return nil, fmt.Errorf("无效的memberId: %d", in.MemberId)
	}

	// 查询会员标签关联
	var memberTagMaps []model.MemberTagMap
	if err := l.svcCtx.DB.Where("member_id = ?", in.MemberId).Find(&memberTagMaps).Error; err != nil {
		return nil, fmt.Errorf("查询会员标签关联失败: %v", err)
	}

	// 如果没有找到标签关联
	if len(memberTagMaps) == 0 {
		return &member.ListTagsResp{
			Total: 0,
			List:  []*member.TagInfo{},
		}, nil
	}

	// 提取所有标签ID
	var tagIDs []uint
	for _, tagMap := range memberTagMaps {
		tagIDs = append(tagIDs, tagMap.TagID)
	}

	// 查询标签详情
	var tags []model.MemberTag
	if err := l.svcCtx.DB.Where("id IN ?", tagIDs).Find(&tags).Error; err != nil {
		return nil, fmt.Errorf("查询标签详情失败: %v", err)
	}

	// 转换为响应格式
	tagInfos := make([]*member.TagInfo, 0, len(tags))
	for _, tag := range tags {
		tagInfos = append(tagInfos, &member.TagInfo{
			Id:         int64(tag.ID),
			MerchantId: int64(tag.MerchantID),
			StoreId:    int64(tag.StoreID),
			Title:      tag.Title,
			Sort:       int32(tag.Sort),
			Status:     int32(tag.Status),
			CreatedAt:  int64(tag.CreatedAt),
			UpdatedAt:  int64(tag.UpdatedAt),
		})
	}

	return &member.ListTagsResp{
		Total: int64(len(tagInfos)),
		List:  tagInfos,
	}, nil
}
