Name: user.rpc
ListenOn: 0.0.0.0:9001

# 配置etcd
Etcd:
  Hosts:
  - etcd:2379
  Key: user.rpc

# 配置日志
Log:
  Mode: console
  Level: info

#  连接到docker里面的mysql数据库
Mysql:
  DataSource: root:123456@tcp(mysql:3306)/base_db?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

# 配置redis
CacheRedis:
  - Host: redis:6379
    Pass:
    Type: node

# 添加JWT鉴权配置
Auth:
  AccessSecret: "123456890"
  AccessExpire: 3600

# 会员服务的RPC配置
mp_rpc:
  etcd:
    hosts:
      - etcd:2379
    key: member.rpc
  timeout: 30000 # 增加超时时间到30秒