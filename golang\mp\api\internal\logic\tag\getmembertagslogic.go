package tag

import (
	"context"
	"fmt"
	"mp/rpc/member"

	"mp/api/internal/svc"
	"mp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMemberTagsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取会员标签
func NewGetMemberTagsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberTagsLogic {
	return &GetMemberTagsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMemberTagsLogic) GetMemberTags(req *types.GetMemberTagsReq) (resp *types.CommonResponse, err error) {
	// 参数检查
	if req.MemberId <= 0 {
		return &types.CommonResponse{
			Code:    400,
			Message: "会员ID不能为空或小于等于0",
		}, nil
	}

	// 调用RPC服务获取会员标签
	rpcResp, err := l.svcCtx.MemberRpc.GetMemberTags(l.ctx, &member.GetMemberTagsReq{
		MemberId: req.MemberId,
	})
	if err != nil {
		return &types.CommonResponse{
			Code:    500,
			Message: fmt.Sprintf("获取会员标签失败: %v", err),
		}, nil
	}

	// 构建返回数据
	return &types.CommonResponse{
		Code:    200,
		Message: "获取会员标签成功",
		Data: map[string]interface{}{
			"total": rpcResp.Total,
			"list":  rpcResp.List,
		},
	}, nil
}
