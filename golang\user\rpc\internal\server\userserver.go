// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: user.proto

package server

import (
	"context"

	"user/rpc/internal/logic"
	"user/rpc/internal/svc"
	"user/rpc/user"
)

type UserServer struct {
	svcCtx *svc.ServiceContext
	user.UnimplementedUserServer
}

func NewUserServer(svcCtx *svc.ServiceContext) *UserServer {
	return &UserServer{
		svcCtx: svcCtx,
	}
}

// Register creates a new user
func (s *UserServer) Register(ctx context.Context, in *user.RegisterRequest) (*user.UserResponse, error) {
	l := logic.NewRegisterLogic(ctx, s.svcCtx)
	return l.Register(in)
}

// Login authenticates a user and returns user information with token
func (s *UserServer) Login(ctx context.Context, in *user.LoginRequest) (*user.UserResponse, error) {
	l := logic.NewLoginLogic(ctx, s.svcCtx)
	return l.Login(in)
}

// GetUser retrieves user information by ID
func (s *UserServer) GetUser(ctx context.Context, in *user.GetUserRequest) (*user.UserResponse, error) {
	l := logic.NewGetUserLogic(ctx, s.svcCtx)
	return l.GetUser(in)
}
