package model

import (
	"time"

	"gorm.io/gorm"
)

type Dicts struct {
	ID         uint           `gorm:"primaryKey;type:uint;column:id" json:"id"`
	TenantId   uint           `gorm:"type:uint;column:tenantId" json:"tenant_id"`
	CategoryId uint           `gorm:"type:uint;column:category_id" json:"category_id"`
	Code       int            `gorm:"type:varchar(100);column:code" json:"code"`
	Name       string         `gorm:"type:varchar(100);column:name" json:"name"`
	ShowType   string         `gorm:"type:varchar(20);column:show_type" json:"show_type"`
	Remark     string         `gorm:"type:text;column:remark" json:"remark"`
	Status     int            `gorm:"type:bigint;column:status" json:"status"`
	CreatedBy  uint           `gorm:"type:bigint;column:created_by" json:"created_by"`
	UpdatedBy  uint           `gorm:"type:bigint;column:updated_by" json:"updated_by"`
	DeletedBy  uint           `gorm:"type:bigint;column:deleted_by" json:"deleted_by"`
	CreatedAt  time.Time      `gorm:"type:datetime;column:created_time" json:"created_time"`
	UpdatedAt  *time.Time     `gorm:"type:datetime;column:updated_time" json:"updated_time"`
	DeletedAt  gorm.DeletedAt `gorm:"type:datetime;column:deleted_time" json:"deleted_time"`
}

// 指定生成的表名
func (Dicts) TableName() string {
	return "dict"
}
